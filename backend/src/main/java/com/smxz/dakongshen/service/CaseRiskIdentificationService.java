package com.smxz.dakongshen.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepoove.poi.XWPFTemplate;
import com.smxz.commons.exception.BusinessException;
import com.smxz.dakongshen.dto.*;
import com.smxz.dakongshen.dto.response.HistoricalVisitRecords;
import com.smxz.dakongshen.dto.response.PersonInfoResponse;
import com.smxz.dakongshen.dto.response.PersonRiskResponse;
import com.smxz.dakongshen.dto.response.RecommendedCase;
import com.smxz.dakongshen.entity.CaseRiskIdentificationHistory;
import com.smxz.dakongshen.entity.Petition;
import com.smxz.dakongshen.entity.PetitionerPortrait;
import com.smxz.dakongshen.entity.PetitionerTag;
import com.smxz.dakongshen.mapper.CaseRiskIdentificationHistoryMapper;
import com.smxz.dakongshen.mapper.PetitionMapper;
import com.smxz.dakongshen.mapper.PetitionerPortraitMapper;
import com.smxz.dakongshen.mapper.PetitionerTagMapper;
import com.smxz.dakongshen.util.SearchStringUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
public class CaseRiskIdentificationService {

    private static final Map<String, Integer> tagLevelMap = Map.of("high", 0, "medium", 1, "low", 2);

    private static final Comparator<PetitionerTag> comparator = Comparator.comparingInt(a -> tagLevelMap.get(a.getLevel()));

    private static final String DEFAULT_HISTORY_SUMMARY = "该来访人来访首次来访，暂时没有信访风险。检察官按正常接访流程进行接访即可。";

    private static final String DEFAULT_TAG_SUMMARY = "该来访人来访首次来访，暂时没有信访风险。检察官按正常接访流程进行接访即可。";

    private static final String DEFAULT_RISK_LEVEL = "low";

    private static final String NO_DATA = "暂无数据";

    private static final RestClient restClient = RestClient.create();

    @Value("${recommended-case.base-url}")
    private String recommendedCaseBaseUrl;

    @Value("${recommended-case.search.url}")
    private String searchUrl;

    @Value("${recommended-case.detail.url}")
    private String detailUrl;

    @Autowired
    private PetitionMapper petitionMapper;

    @Autowired
    private PetitionerTagMapper petitionerTagMapper;

    @Autowired
    private PetitionerPortraitMapper petitionerPortraitMapper;

    @Autowired
    private CaseRiskIdentificationHistoryMapper caseRiskIdentificationHistoryMapper;

    public PersonInfoResponse search(String searchContent) {
        SearchInfo searchInfo = SearchStringUtils.analyse(searchContent);

        var searchHistory = new CaseRiskIdentificationHistory();
        searchHistory.setSearchContent(searchContent);
        searchHistory.setCreateTime(LocalDateTime.now());
        caseRiskIdentificationHistoryMapper.insert(searchHistory);
        PetitionerPortrait portrait = petitionerPortraitMapper.selectOne(Wrappers.<PetitionerPortrait>lambdaQuery().eq(PetitionerPortrait::getPetitionerName, searchInfo.getName()), false);

        // mock数据
        PersonInfoResponse response = new PersonInfoResponse();
        response.setName(searchInfo.getName());
        response.setIdNumber(searchInfo.getIdNumber());
        response.setBrief(searchInfo.getCaseInfo());
        response.setSex(NO_DATA);
        response.setContactAddress(NO_DATA);
        response.setRegisteredResidence(NO_DATA);
        response.setRiskLevel(portrait == null ? DEFAULT_RISK_LEVEL : portrait.getRiskLevel());

        return response;
    }

    public PersonRiskResponse generateTagsAndSummary(String idNumber, String name) {
        LambdaQueryWrapper<PetitionerPortrait> lambdaQueryWrapper = Wrappers.<PetitionerPortrait>lambdaQuery()
                .eq(PetitionerPortrait::getPetitionerName, name);
        PetitionerPortrait portrait = petitionerPortraitMapper.selectOne(lambdaQueryWrapper, false);
        PersonRiskResponse result = new PersonRiskResponse();
        if (portrait == null) {
            result.setTags(Collections.emptyList());
            result.setSummary(DEFAULT_TAG_SUMMARY);
            return result;
        }

        LambdaQueryWrapper<PetitionerTag> tagLambdaQueryWrapper = Wrappers.<PetitionerTag>lambdaQuery()
                .eq(PetitionerTag::getPetitionerId, portrait.getId());
        List<PetitionerTag> tags = petitionerTagMapper.selectList(tagLambdaQueryWrapper);
        result.setTags(tags);
        if (StringUtils.isNotBlank(portrait.getTagSummary())) {
            result.setSummary(DEFAULT_TAG_SUMMARY);
        } else {
            result.setSummary(portrait.getTagSummary());
        }

        return result;
    }

    public List<CaseRiskIdentificationHistory> listHistory() {
        LambdaQueryWrapper<CaseRiskIdentificationHistory> wrapper = Wrappers.<CaseRiskIdentificationHistory>lambdaQuery()
                .orderByDesc(CaseRiskIdentificationHistory::getId)
                .last("LIMIT 5");

        return caseRiskIdentificationHistoryMapper.selectList(wrapper);
    }

    public List<RecommendedCase> listRecommendedCase(String brief) {
        RecommendedCaseInfoRequest request = new RecommendedCaseInfoRequest();
        request.setQuery(brief);
        request.setPage(1);

        SifazhushouResult<List<RecommendedCaseInfo>> result = restClient.post().uri(recommendedCaseBaseUrl + searchUrl)
                .header("token", "a12c62e53510577fa307abebb65d2458")
                .body(request)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });

        List<RecommendedCaseInfo> caseInfos = result == null ? Collections.emptyList() : result.getData();

        return caseInfos.stream()
                .map(e -> {
                    RecommendedCase r = new RecommendedCase();
                    BeanUtils.copyProperties(e, r);
                    return r;
                })
                .toList();
    }

    public RecommendedCaseDetail getRecommendedCaseDetail(String wenshuId) {
        SifazhushouResult<RecommendedCaseDetail> caseDetail = restClient.get().uri(recommendedCaseBaseUrl + detailUrl + "?wenshu_id={wenshuId}", wenshuId)
                .header("token", "a12c62e53510577fa307abebb65d2458")
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });

        return caseDetail == null ? null : caseDetail.getData();
    }

    public HistoricalVisitRecords listHistoricalVisitRecord(String idNumber, String name) {
        LambdaQueryWrapper<PetitionerPortrait> lambdaQueryWrapper = Wrappers.<PetitionerPortrait>lambdaQuery()
                .eq(PetitionerPortrait::getPetitionerName, name);
        PetitionerPortrait portrait = petitionerPortraitMapper.selectOne(lambdaQueryWrapper, false);

        HistoricalVisitRecords result = new HistoricalVisitRecords();
        if (portrait == null) {
            result.setVisitRecords(Collections.emptyList());
            result.setSummary(DEFAULT_HISTORY_SUMMARY);
            return result;
        }

        List<Petition> petitions = petitionMapper.listPetitionByPetitionerName(name);
        List<HistoricalVisitRecords.HistoricalVisitRecord> records = petitions.stream()
                .map(e -> {
                    var r = new HistoricalVisitRecords.HistoricalVisitRecord();
                    r.setVisitTime(e.getPetitionTime());
                    r.setCaseType(e.getPetitionType());
                    r.setBrief(e.getBrief());
                    r.setOrganizer(e.getOrganizer());
                    r.setCaseName(e.getCaseName());
                    r.setPetitionChannel(e.getPetitionChannel());
                    r.setReplyContent("暂无数据");

                    return r;
                }).toList();

        result.setVisitRecords(records);
        if (StringUtils.isNotBlank(portrait.getPetitionHistorySummary())) {
            result.setSummary(DEFAULT_HISTORY_SUMMARY);
        } else {
            result.setSummary(portrait.getPetitionHistorySummary());
        }

        return result;
    }

    public void download(String name, String idNumber, HttpServletResponse response) {
        PetitionerPortrait portrait = petitionerPortraitMapper.selectOne(Wrappers.<PetitionerPortrait>lambdaQuery().eq(PetitionerPortrait::getPetitionerName, name), false);
        if (portrait == null) {
            throw new BusinessException("下载失败");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("name", portrait.getPetitionerName());
        map.put("sex", NO_DATA);
        map.put("idNumber", idNumber);
        map.put("contactAddress", NO_DATA);
        map.put("registeredResidence", NO_DATA);
        map.put("historySummary", portrait.getPetitionHistorySummary());
        map.put("tagSummary", portrait.getTagSummary());

        MediaTypeFactory.getMediaTypes("woldTemplate.docx").stream().findFirst().ifPresent(e -> {
            response.setContentType(e.getType());
        });

        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("风险评估报告.docx", StandardCharsets.UTF_8));
        try (InputStream inputStream = this.getClass().getResourceAsStream("/template/woldTemplate.docx");
             OutputStream outputStream = response.getOutputStream();
             XWPFTemplate template = XWPFTemplate.compile(inputStream).render(map)) {
            template.write(outputStream);
        } catch (IOException e) {
            response.setContentType(null);
            response.setHeader("Content-disposition", null);
            throw new RuntimeException(e);
        }
    }
}
