/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.service.tyyw;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.dakongshen.entity.tyyw.Ajjzwj;
import com.smxz.dakongshen.mapper.tyyw.JzmlwjMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> 诚
 * @description
 * @since 2025-05-29 16:27
 **/
@Service
public class JzmlwjService extends ServiceImpl<JzmlwjMapper, Ajjzwj> {




        public List<Ajjzwj> listAjjzwj(LocalDate startDate, LocalDate endDate){
                LambdaQueryWrapper<Ajjzwj> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.between(Ajjzwj::getZhxgsj, startDate, endDate);
                queryWrapper.eq(Ajjzwj::getSfsc, "N");
                queryWrapper.in(Ajjzwj::getWsmbbh,"100000990642","100000990643","100000990644");
                return list(queryWrapper);
        }

        public List<Ajjzwj> listAjjzwj(LocalDateTime startDate, LocalDateTime endDate){
                LambdaQueryWrapper<Ajjzwj> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.between(Ajjzwj::getZhxgsj, startDate, endDate);
                queryWrapper.eq(Ajjzwj::getSfsc, "N");
                queryWrapper.in(Ajjzwj::getWsmbbh,"100000990642","100000990643","100000990644");
                return list(queryWrapper);
        }

}
