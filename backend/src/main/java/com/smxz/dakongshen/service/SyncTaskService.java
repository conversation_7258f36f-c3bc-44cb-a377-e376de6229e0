/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.dakongshen.consts.TaskStatus;
import com.smxz.dakongshen.consts.WritSourceEnum;
import com.smxz.dakongshen.entity.WritHandlerEntity;
import com.smxz.dakongshen.entity.tyyw.Ajjzwj;
import com.smxz.dakongshen.entity.SyncTask;
import com.smxz.dakongshen.handler.DocxToPdfHandler;
import com.smxz.dakongshen.mapper.SyncTaskMapper;
import com.smxz.dakongshen.service.tyyw.JzmlwjService;
import com.smxz.dakongshen.service.tyyw.SyncTyywAjxxBaseService;
import com.smxz.ragflow.model.BaseResponse;
import com.smxz.ragflow.model.GeneralListRequest;
import com.smxz.ragflow.model.assistant.ChatAssistantCreateRequest;
import com.smxz.ragflow.model.assistant.ChatAssistantData;
import com.smxz.ragflow.model.dataset.DatasetCreateRequest;
import com.smxz.ragflow.model.dataset.DatasetData;
import com.smxz.ragflow.model.document.DocumentData;
import com.smxz.ragflow.model.document.DocumentListWapper;
import com.smxz.ragflow.model.document.DocumentUpdateRequest;
import com.smxz.ragflow.model.document.DocumentUploadRequest;
import com.smxz.ragflow.model.session.*;
import com.smxz.ragflow.service.*;
import com.smxz.smxzpromptmanagespringbootstarter.entity.PromptConfig;
import com.smxz.smxzpromptmanagespringbootstarter.service.PromptConfigService;
import io.minio.*;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR> 诚
 * @description
 * @since 2025-05-28 14:41
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class SyncTaskService extends ServiceImpl<SyncTaskMapper, SyncTask>  {


    private final WritHandleManager writHandleManager;

    private final JzmlwjService jzmlwjService;

    private final DatasetService datasetService;

    private final RagflowDocumentService documentService;

    private final ChatAssistantService chatAssistantService;

    private final SessionService sessionService;

    private final DocxToPdfHandler docxToPdfHandler;

    private final SyncTyywAjxxBaseService syncTyywAjxxBaseService;

    private final PromptConfigService promptConfigService;

    @Value("${writ.source}")
    private WritSourceEnum sourceEnum;


    @Value("${xffxsb.database-id}")
    private String fxsbDatasetId;

    @Value("${xffxsb.model-name}")
    private String fxsbModelName;

    /**
     * 将日期范围拆分成每月任务
     */
    public static List<SyncTask> splitIntoMonthlyTasks(LocalDate startDate, LocalDate endDate) {
        List<SyncTask> tasks = new ArrayList<>();

        LocalDate currentStart = startDate;
        LocalDate currentEnd;
        while (currentStart.isBefore(endDate)) {
            // 计算当前月的最后一天
            currentEnd = currentStart.withDayOfMonth(currentStart.lengthOfMonth());
            // 如果当前月的最后一天超过结束日期，则使用结束日期
            if (currentEnd.isAfter(endDate)) {
                currentEnd = endDate;
            }
            tasks.add(SyncTask.builder().
                    startDate(currentStart).
                    endDate(currentEnd)
                    .status(TaskStatus.INIT.getCode()).build());
            // 移动到下个月的第一天0
            currentStart = currentEnd.plusDays(1);
        }
        return tasks;
    }


    public void startSyncTask(SyncTask syncTask){
        beginSyncTask(syncTask);
        // 创建知识库
        List<Ajjzwj> ajjzwjs = jzmlwjService.listAjjzwj(syncTask.getStartDate(), syncTask.getEndDate());
        log.info("{}至{}共计查询到{}条数据",syncTask.getStartDate(),syncTask.getEndDate(),ajjzwjs.size());
        syncTask.setTotalCount(ajjzwjs.size());
        syncTask.setSuccessCount(0);
        if(CollectionUtils.isNotEmpty(ajjzwjs)) {
            try {
                long start = System.currentTimeMillis();
                batchAnalyzeAjjzwj(ajjzwjs, syncTask);
                long end = System.currentTimeMillis();
                log.info("文件批量分析成功，共计{}个文件，其中{}有风险，共耗时{}ms,平均单个解析耗时{}ms", ajjzwjs.size(), syncTask.getSuccessCount(), end - start, (end - start) / ajjzwjs.size());
            } catch (IOException e) {
                log.error("同步任务失败,异常信息为：{}", e.getMessage());
                throw new RuntimeException(e);
            }
        }
        finishSyncTask(syncTask);
    }

    public void batchAnalyzeAjjzwj(List<Ajjzwj> ajjzwjs,SyncTask syncTask) throws IOException {
        if (CollectionUtils.isNotEmpty(ajjzwjs)){
            for (Ajjzwj ajjzwj : ajjzwjs) {
                if(StringUtils.isAnyEmpty(ajjzwj.getZbqfwqwjmc(),ajjzwj.getZbqwjcflj())){
                    log.warn("文件信息有误，文件信息为：{}", JSON.toJSONString(ajjzwj));
                }
                String path = ajjzwj.getZbqwjcflj().replace("yxsj", "");
                String fullPath = FilenameUtils.normalize(FilenameUtils.concat(path, ajjzwj.getZbqfwqwjmc()),true);
                IWritHandlerService service = writHandleManager.getHandlerService(sourceEnum);
                Optional<InputStream> download = service.download(WritHandlerEntity.builder().fileFullPath(fullPath).build());
                if (download.isEmpty()) {
                    log.warn(" SFTP下载文件失败，文件信息为：{}", JSON.toJSONString(ajjzwj));
                    continue;
                }
                InputStream inputStream = download.get();
                byte[] fileData = inputStream.readAllBytes(); // 一次性读取
                try(ByteArrayInputStream ins4Rag =new ByteArrayInputStream(fileData);
                    ByteArrayInputStream ins4Minio =new ByteArrayInputStream(fileData);
                ) {
                    StopWatch stopWatch = new StopWatch("分析审查报告");
                    stopWatch.start("分析审查报告");
                    RagAnswer ragAnswer = doAnalysis(ajjzwj,ins4Rag);
                    stopWatch.stop();
                    log.info(stopWatch.prettyPrint());
                    if (ObjectUtils.isEmpty(ragAnswer)){
                        log.error("知识库分析风险信息ragAnswer为null,文件信息为 {}",ajjzwj);
                        continue;
                    }
                    JSONObject parsed = JSON.parseObject(ragAnswer.getAnswer());
                    if (parsed.containsKey("res")&& parsed.getBoolean("res")){
                        log.info("部门受案号{}文件{}涉及信访风险",ajjzwj.getYxslbh(),ajjzwj.getZbqfwqwjmc());
                        if ((ObjectUtils.isNotEmpty(syncTask))){
                            syncTask.setSuccessCount(syncTask.getSuccessCount() + 1);
                        }
                        //TODO:上传文件到minio

                        ObjectWriteResponse response = docxToPdfHandler.doUploadFile( ins4Minio, ajjzwj.getZbqfwqwjmc());

                        ObjectWriteResponse pdf = docxToPdfHandler.doc2PdfWithUpload(response.object(), ajjzwj.getZbqfwqwjmc());

                        syncTyywAjxxBaseService.startSyncAjxx(ajjzwj,ragAnswer,pdf.object());
                    }
                }catch ( Exception e){
                    log.error("风险分析出现异常",e);
                }
                // 分析 审查包括 是否涉及信访风险

            }
        }


    }

    private void beginSyncTask(SyncTask syncTask) {
        syncTask.setStatus(TaskStatus.PROCESSING.getCode());
        syncTask.setBeginTime(LocalDateTime.now());
        this.updateById(syncTask);
    }
    private void finishSyncTask(SyncTask syncTask) {
        syncTask.setStatus(TaskStatus.FINISHED.getCode());
        syncTask.setFinishTime(LocalDateTime.now());
        this.updateById(syncTask);
    }


    /**
     * 分析之后 进行的 清理工作
     */
    private void  afterAnalysis(String chatId,String datasetId){
        log.info("开始进行清理工作,助手id为：{}，知识库id为：{}",chatId,datasetId);
        //删除助手
        chatAssistantService.deleteAssistant(List.of(chatId));
        log.info("助手删除成功,助手id为：{}",chatId);
        //删除知识库
        datasetService.deleteDataset(List.of(datasetId));
        log.info("临时知识库删除成功,知识库id为：{}",datasetId);
    }

    /**
     * 分析 审查包括 是否涉及信访风险
     * 创建知识库  --->    上传文件到知识库  --->  解析文件  --->  创建助理  --->  创建会话  --->  聊天  --->  分析结果 --> 清理资源
     * @param ajjzwj
     * @param ins
     * @return
     */
    public RagAnswer doAnalysis(Ajjzwj ajjzwj,InputStream ins){
        String datasetId = null;
        String chatId = null;
        RagAnswer answer = null;
        try {
            // 给单独一个文件创建知识库
            BaseResponse<DatasetData> dataset = datasetService.createDataset(DatasetCreateRequest.builder().name(ajjzwj.getZbqfwqwjmc()+System.currentTimeMillis()).build());
            if (dataset != null && dataset.getCode() == 0){
                log.info("知识库创建成功,知识库信息为：{}",dataset.getData());
            }else {
                log.error("知识库创建失败,错误信息为：{}",dataset);
                throw new RuntimeException("知识库创建失败");
            }

            datasetId = dataset.getData().getId();
            // 上传文件到知识库
            DocumentUploadRequest uploadRequest = DocumentUploadRequest.builder().
                    fileName(ajjzwj.getZbqfwqwjmc()).
                    file(ins).datasetId(datasetId).build();
            BaseResponse<List<DocumentData>> uploaded = documentService.uploadDocument(uploadRequest);
            log.info("文件上传成功,文件信息为：{}",uploaded);

            // 解析文件
            BaseResponse<Void> response = documentService.parseDocument(datasetId, uploaded.getData().stream().map(DocumentData::getId).toList());
            log.info("解析文件的响应为{}",response);
            DocumentData documentData;
            do{
                BaseResponse<DocumentListWapper> documents = documentService.listDocuments(datasetId, GeneralListRequest.builder().id(uploaded.getData().stream().map(DocumentData::getId).toList().get(0)).build());
                documentData = documents.getData().getDocs().get(0);
                log.info("文件解析中,文件信息为：{}",documentData);
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }while (!StringUtils.equals(documentData.getRun(),"DONE"));
            PromptConfig systemPrompt = promptConfigService.getLastesByCategoryKeyAndKey("xffxytsbyfk", "systemPrompt");
            //创建助理

            List<String> datasetIds = Lists.newArrayList();
            if(StringUtils.isNoneEmpty(fxsbDatasetId)){
                datasetIds.addAll(Arrays.asList(StringUtils.split(fxsbDatasetId,";")));
            }
            datasetIds.add(datasetId);
            ChatAssistantCreateRequest assistantCreateRequest = ChatAssistantCreateRequest.builder().name("信访风险识别助手"+System.currentTimeMillis()).description("根据审查报告，识别是否涉及信访风险").datasetIds(datasetIds).build();
            assistantCreateRequest.addLlmSetting("model_name",fxsbModelName);
            assistantCreateRequest.addPromptSetting("empty_response","");
            assistantCreateRequest.addPromptSetting("prompt",systemPrompt.getContent());
            assistantCreateRequest.addPromptSetting("show_quote",false);


            BaseResponse<ChatAssistantData> assistant = chatAssistantService.createAssistant(assistantCreateRequest);
            log.info("助理创建成功,会话信息为：{}",assistant);
            chatId = assistant.getData().getId();

            //创建会话
            BaseResponse<SessionData> session = sessionService.createSessionWithChat(chatId, SessionCreateRequest.builder().name("风险识别Robot").userId("robot").build());
            log.info("知识库Session创建成功,会话信息为：{}",session);
            String sessionId = session.getData().getId();
            PromptConfig userPrompt = promptConfigService.getLastesByCategoryKeyAndKey("xffxytsbyfk", "userPrompt");
            log.info("用户提示词{}",userPrompt);
            BaseResponse<RagAnswer> ragAnswerBaseResponse = sessionService.converseWhitChatAssistant(chatId, RagQuestion.builder().
                    sessionId(sessionId).question(userPrompt.getContent()).build());
            log.info("知识库聊天对话创建成功,知识库返回信息为：{}",ragAnswerBaseResponse);

            answer = ragAnswerBaseResponse.getData();

        } catch (RuntimeException e) {
            log.error("审查报告知识库解析失败,错误信息为，案件信息为：{}",ajjzwj,e);
        } finally {
            this.afterAnalysis(chatId,datasetId);

        }
        return answer;
    }






    public static String prompt = """
你是一个智能助手，知识库中只有一个审查报告，请根据我提供的标签总结知识库的内容来回答问题，包含以下任意一个都视为有风险，我需要你返回该案件当事人是否有信访风险
标签如下：
犯罪嫌疑人拒不认罪；
被害人遭受严重人身伤害或者重大财产损失且未获赔偿；
双方未达成和解（未签署和解协议或者未签署赔偿协议）；
双方矛盾难以调和；
双方矛盾尖锐；
检察机关作出不批准逮捕、不起诉决定或者不支持立案监督申请的；
不支持其申请；
不支持其诉求；
涉访敏感案件；
不服处理结果；
认为量刑畸轻、畸重；
认为司法不公；
认为执法不公正；
认为判决不公正；
同案不同判；
多次来检察机关反映意见；
涉及受害人众多；
身患严重/重大疾病；
存在人员死亡；
诉求长期未得到解决；
威胁性表述、家庭重大变故；
家庭破裂；
情绪失控或行为异常；
存在严重精神障碍；
反映司法人员违法违纪；
发生网络舆情；
在社交媒体、公共场合或与他人交流中发表极端言论；
无固定职业或无固定经济收入，生活困难；
社交媒体关注大量暴力、恐怖主义内容，加入宣扬极端思想的网络群组；
可能引发信访风险。

以下是知识库内容
{knowledge}
以上是知识库内容



""";

    private static String  risk_assessments= """
        {
            "doc_id": "您的文档ID",
            "content": "您的文档内容...",
            "meta_fields": {
                "risk_assessments": [
                    {
                        "序号": "1",
                        "风险类型": "当事人对检察机关的处理结果不服",
                        "细分项": "不服不批准/批准逮捕",
                        "等级": "高",
                        "具体情形": "认为应当逮捕/不应当逮捕",
                        "示例": ""
                    },
                    {
                        "序号": "2",
                        "风险类型": "当事人对检察机关的处理结果不服",
                        "细分项": "不服不起诉/起诉",
                        "等级": "高",
                        "具体情形": "认为应当起诉/不应当不起诉",
                        "示例": ""
                    },
                    {
                        "序号": "3",
                        "风险类型": "当事人对检察机关的处理结果不服",
                        "细分项": "不服定性",
                        "等级": "低",
                        "具体情形": "认为不构成犯罪或者构成犯罪或者认为构成其他罪",
                        "示例": "示例1：犯罪嫌疑人周某涉嫌合同诈骗一案，周某到案后坚称其与被害人签订的合同系正常商务合作，因市场环境变化导致履约困难，不存在非法占有的目的，不构成犯罪。但经研判，周某虽然有信访意向，但本人性格相对理性，其律师以及家属亦表示愿意配合司法机关工作。"
                    },
                    {
                        "序号": "4",
                        "风险类型": "当事人对检察机关的处理结果不服",
                        "细分项": "不服量刑",
                        "等级": "低",
                        "具体情形": "",
                        "示例": "示例1：被害人认为对犯罪嫌疑人的量刑过轻，希望从严惩处犯罪嫌疑人。\\n示例2：犯罪嫌疑人认为太冤枉了，认为量刑太重，不愿意签署认罪认罚具结书。"
                    },
                    {
                        "序号": "5",
                        "风险类型": "当事人对检察机关的处理结果不服",
                        "细分项": "不服事实认定",
                        "等级": "低",
                        "具体情形": "",
                        "示例": "示例1：犯罪嫌疑人李某涉嫌故意伤害罪一案中，李某坚称是被害人先动手，其才与对方打起来的，认为被害人存在一定的过错，希望对其从轻处罚。"
                    },
                    {
                        "序号": "6",
                        "风险类型": "被害人遭受人身伤害或者财产损失且未获赔偿",
                        "细分项": "被害人遭受重大伤害或者巨大财产损失且未获赔偿",
                        "等级": "高",
                        "具体情形": "重大伤害包含：重伤、死亡；\\n巨大财产损失：10万以上",
                        "示例": "示例1：被害人案发后产生医疗费X元,后续康复治疗预估费用为X元,犯罪嫌疑人名下无财产,其家属亦表示无力赔偿,被害人家庭经济十分困难。\\n示例2：案发后,追缴涉案财物折合仅X元,按比例分配后,被害人平均获赔比例不足损失金额的X%,绝大部分损失无法挽回,多名被害人毕生积蓄血本无归,生活陷入困境。"
                    },
                    {
                        "序号": "7",
                        "风险类型": "被害人遭受人身伤害或者财产损失且未获赔偿",
                        "细分项": "被害人遭受人身伤害或者财产损失且未获赔偿",
                        "等级": "低",
                        "具体情形": "",
                        "示例": "示例1：被害人系轻伤二级，被害人目前尚未获得赔偿，多次联系经办人，要求犯罪嫌疑人赔偿其损失。通过前期与犯罪嫌疑人及其家属沟通，了解到有和解的意愿，且犯罪嫌疑人对自身行为表示悔意，只是因经济困难一时难以拿出赔偿款，但其承诺会筹措资金。"
                    },
                    {
                        "序号": "8",
                        "风险类型": "双方未达成和解",
                        "细分项": "双方未签署和解协议或者未签署赔偿协议",
                        "等级": "中",
                        "具体情形": "",
                        "示例": "示例1：在多次调解过程中，双方就赔偿金额及其他和解条件始终未能达成一致。被害人坚持较高的赔偿诉求，并要求犯罪嫌疑人公开赔礼道歉等；犯罪嫌疑人虽有赔偿意愿，但因经济能力有限，无法满足被害人全部要求，导致和解陷入僵局，双方至今未能签署和解协议。"
                    },
                    {
                        "序号": "9",
                        "风险类型": "双方矛盾难以调和或双方矛盾尖锐",
                        "细分项": "双方矛盾难以调和或双方矛盾尖锐",
                        "等级": "高",
                        "具体情形": "",
                        "示例": "示例1：双方积怨较深,经多次调解,双方态度强硬,互不退让,和解难度大。\\n示例2：嫌疑人/被害人态度蛮横,拒不配合调解工作,双方矛盾尖锐,诉求差距悬殊,暂无和解可能。"
                    },
                    {
                        "序号": "10",
                        "风险类型": "犯罪嫌疑人死亡",
                        "细分项": "羁押状态下死亡",
                        "等级": "高",
                        "具体情形": "",
                        "示例": "示例1：犯罪嫌疑人林某在羁押期间突发疾病死亡，虽经看守所及时送医抢救，但仍未能挽回生命。目前，公安机关已启动调查程序，对林某死亡前的监控录像、就医记录等进行封存和调查，同时邀请专业机构进行死因鉴定。然而，林某家属对林某的死亡原因存疑，认为看守所可能存在监管失职或救治不及时等问题，已多次到检察机关信访，要求彻查真相，并表示若得不到满意答复，将继续向上级部门反映，甚至通过媒体曝光等方式扩大影响，存在引发大规模信访事件的风险。"
                    },
                    {
                        "序号": "11",
                        "风险类型": "犯罪嫌疑人死亡",
                        "细分项": "犯罪嫌疑人死亡",
                        "等级": "低",
                        "具体情形": "",
                        "示例": "示例1：在取保候审期间，犯罪嫌疑人在某地发生交通事故当场死亡，经交警部门认定，事故系对方车辆突然变道等原因导致，对方承担全部责任，目前对方已赔偿犯罪嫌疑人家属150万元，已取得犯罪嫌疑人家属的谅解。其家属对事故认定无异议，且了解案件因犯罪嫌疑人死亡将依法处理本案，家属情绪稳定，未提出不合理诉求。"
                    },
                    {
                        "序号": "12",
                        "风险类型": "受害人众多",
                        "细分项": "受害人50人以上",
                        "等级": "高",
                        "具体情形": "",
                        "示例": "示例1：本案犯罪行为持续时间长、波及范围广，经审查，涉及被害人多达54名，涵盖不同年龄、职业群体，地域分布广泛，被害群体具有广泛性与复杂性，社会影响恶劣。"
                    },
                    {
                        "序号": "13",
                        "风险类型": "受害人众多",
                        "细分项": "受害人30-50人",
                        "等级": "中",
                        "具体情形": "",
                        "示例": "示例1：本案犯罪手段隐蔽性强、持续周期长，致使受害人数众多，累计达36人。受害人涉及学生/老年人/企业经营者等具体身份,因信息不对称、防范意识薄弱等原因遭受侵害，案件证据收集、损失核算及权益保障工作面临多重挑战。"
                    },
                    {
                        "序号": "14",
                        "风险类型": "受害人众多",
                        "细分项": "受害人30人以下",
                        "等级": "低",
                        "具体情形": "",
                        "示例": "示例1：本案犯罪行为引发25名被害人集体维权，涉及经济损失总额达40万元，已引发社会广泛关注。案件办理过程中，不仅需依法追究犯罪嫌疑人刑事责任，更需要通过有序引导、信息公开等措施，妥善化解群体性矛盾，维护社会稳定大局。"
                    },
                    {
                        "序号": "15",
                        "风险类型": "重点关注案由",
                        "细分项": "涉传销、非法集资、集资诈骗、非法吸收公众存款、电信诈骗",
                        "等级": "高",
                        "具体情形": "",
                        "示例": "示例1：本案是集资诈骗案，受害群众众多，涉及人数达55人，遍布多个地区，且多数为收入低人群，部分甚至将毕生积蓄投入其中，血本无归，这些受害群众情绪较为激动，通过线上社交群组紧密联系，已出现多次集体沟通维权的情况。"
                    },
                    {
                        "序号": "16",
                        "风险类型": "重点关注案由",
                        "细分项": "涉黑涉恶、涉邪教",
                        "等级": "中",
                        "具体情形": "",
                        "示例": "示例1：此次办理的涉黑案件，犯罪嫌疑人长期在本地实施一系列违法犯罪活动，涉及敲诈勒索、聚众斗殴、开设赌场等多项罪名，严重扰乱了当地的经济和社会生活秩序，给众多群众造成了较大的身心伤害和财产损失。案件曝光后，已引起社会各界广泛关注，被害人要求严惩犯罪嫌疑人，对案件办理过程中的各个环节都高度关注。"
                    },
                    {
                        "序号": "17",
                        "风险类型": "网络舆情风险",
                        "细分项": "已产生舆情",
                        "等级": "高",
                        "具体情形": "",
                        "示例": "示例1：微信公众号**发文质疑本案“存在刑讯逼供”。\\n示例2：抖音用户**发视频称“本案有保护伞”。"
                    },
                    {
                        "序号": "18",
                        "风险类型": "网络舆情风险",
                        "细分项": "未产生舆情",
                        "等级": "中",
                        "具体情形": "",
                        "示例": "示例1：本案案件性质敏感，且犯罪嫌疑人系公职人员，案件细节一旦泄露，可能在网络上迅速发酵，形成舆论热点。目前尚未出现网络舆情，但不排除后续因未能达成和解引发网络舆情。"
                    },
                    {
                        "序号": "19",
                        "风险类型": "当事人发表极端言论",
                        "细分项": "",
                        "等级": "高",
                        "具体情形": "",
                        "示例": "示例1：被害人孙某多次来本院、致电本院信访，情绪激动，本院通过检察官现场接待、公开听证等方式，对案件的办理、证据认定向其进行了全面释法说理，但其仍持续致电本院吵闹，要求本院尽快起诉犯罪嫌疑人李某，扬言“跳楼自杀”，2022年12月7日还扬言“炸检察院”。\\n示例2：如果我的钱不能讨回来，我就去跳楼自杀。"
                    },
                    {
                        "序号": "20",
                        "风险类型": "社交媒体关注大量暴力、恐怖主义内容，加入宣扬极端思想的网络群组",
                        "细分项": "",
                        "等级": "高",
                        "具体情形": "",
                        "示例": "示例1：经核查，犯罪嫌疑人张某手机社交平台历史记录显示，其近6个月内持续关注多个宣扬暴力、恐怖主义的账号，累计点赞、转发相关内容500余次。\\n示例2：被害人王某手机社交平台记录显示，其近3个月关注多个“复仇教程”“暴力维权”类账号。"
                    },
                    {
                        "序号": "21",
                        "风险类型": "被害人或者犯罪嫌疑人多次向检察机关反映意见",
                        "细分项": "",
                        "等级": "高",
                        "具体情形": "",
                        "示例": "示例1：当事人多次前来检察机关反映对案件处理结果的意见，其主要观点为...。\\n示例2：作为本案犯罪嫌疑人或者被害人，近期多次到检察机关反映意见，声称案件存在....问题。"
                    },
                    {
                        "序号": "22",
                        "风险类型": "被害人或者犯罪嫌疑人情绪失控或行为异常",
                        "细分项": "",
                        "等级": "高",
                        "具体情形": "",
                        "示例": "示例1：犯罪嫌疑人王某在讯问过程中情绪激动，多次打断讯问人员提问，高声喊叫“我没罪，是他们逼我的”，并拍打讯问椅扶手。\\n示例2：在被害人王某被诈骗一案中，被害人王某因案件办理过程中部分证据调取周期较长，认为司法机关存在拖延办案的情况，多次通过电话、来访方式质问案件办理进度，情绪逐渐失控。王某表示若案件不能尽快处理，将在开庭时邀请媒体旁听并公开个人遭遇，同时联合类似案件受害者集体到政府机关上访。"
                    },
                    {
                        "序号": "23",
                        "风险类型": "被害人或者犯罪嫌疑人存在严重精神障碍",
                        "细分项": "",
                        "等级": "中",
                        "具体情形": "",
                        "示例": "示例1：经鉴定，犯罪嫌疑人患有**​（具体精神疾病名称，比如精神分裂症），主要表现为**​（症状描述，如持续性幻听、被害妄想、行为紊乱等）。\\n示例2：被害人患有重度精神发育迟滞，IQ测试结果为45,无法理解复杂指令和社会规则。"
                    },
                    {
                        "序号": "24",
                        "风险类型": "被害人或者犯罪嫌疑人身患严重/重大疾病",
                        "细分项": "",
                        "等级": "中",
                        "具体情形": "",
                        "示例": "示例1：在陈某被诈骗一案中，被害人陈某被骗取毕生积蓄后，突发急性心机梗死，虽经抢救保住性命，但需长期服药并定期进行心脏支架维护治疗，后续医疗费用缺口巨大。陈某认为检察机关办案进度缓慢，未能及时挽回其经济损失，多次情绪激动地到检察机关信访，称若无法尽快追回被骗款项支付医疗费，将放弃治疗并向办案人员“讨说法”。  \\n示例2：犯罪嫌疑人李某取保候审期间突发脑溢血导致半身瘫痪，其子女认为李某已丧失社会危险性，多次到检察机关、政法委要求撤销案件，让李某回老家照料。"
                    },
                    {
                        "序号": "25",
                        "风险类型": "受害人及近亲属诉求长期未得到解决",
                        "细分项": "",
                        "等级": "高",
                        "具体情形": "",
                        "示例": "示例1：因犯罪嫌疑人突发重大疾病需长期治疗，导致案件无法审结，犯罪嫌疑人无赔偿能力，被害人的损失无法弥补，被害人长期向检察机关提出的诉求无法满足。\\n示例2：当事人长期反映的诉求系陈年旧案，原始案件材料因历史原因存在缺失，原办案人员已退休或调离，增加了情况核查难度。"
                    },
                    {
                        "序号": "26",
                        "风险类型": "家庭破裂、家庭重大变故",
                        "细分项": "",
                        "等级": "中",
                        "具体情形": "",
                        "示例": "示例1：犯罪嫌疑人李某父亲于**年因车祸去世，母亲随后改嫁，李某由祖父母抚养长大。案发前，祖父母因年迈无力照料李某，李某辍学后长期失业，经济来源主要依靠低保。李某多次表示“家里没人管我，我就想干票大的。”\\n示例2：被害人刘某的目前于**去世，其作为独子需独自赡养父亲的责任，案发前因照顾父亲失业，家庭经济陷入困境。"
                    },
                    {
                        "序号": "27",
                        "风险类型": "无固定职业或无固定经济收入，生活困难",
                        "细分项": "",
                        "等级": "中",
                        "具体情形": "",
                        "示例": "示例1：无固定收入/无经济收入/无稳定收入来源/打零工/低保户/家庭负债/依靠亲友接济度日/生活贫困/生活陷入困难\\n示例2：犯罪嫌疑人李某无固定职业，长期靠打零工维持生计，近半年因工地失业，家庭负债10万元。"
                    },
                    {
                        "序号": "28",
                        "风险类型": "涉及信访风险关键词",
                        "细分项": "舆情、信访、上访、极端、扬言、跳楼、自杀、自残、同归于尽、血溅三尺、冤枉、冤屈、不公、风险、恐怖主义、访情、进京、涉访、敏感、三同步",
                        "等级": "高",
                        "具体情形": "",
                        "示例": "示例1：被害人唐某在听取被害人意见时有极端言论，称经济困难，要求检察机关为其追赃挽损，不然其活不下去，要去跳楼，和检察官同归于尽、玉石俱焚。"
                    }
                ],
                "document_type": "风险评估报告",
                "creation_date": "2023-10-26"
            }
        }
        
        """;
}
