package com.smxz.dakongshen.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.smxz.dakongshen.entity.User;
import com.smxz.dakongshen.mapper.UserMapper;
import com.smxz.dakongshen.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public User findByLoginId(String loginId) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getLoginId, loginId);
        return userMapper.selectOne(queryWrapper);
    }
} 