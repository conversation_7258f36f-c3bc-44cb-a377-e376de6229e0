package com.smxz.dakongshen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName("petition_petitioner")
public class PetitionPetitioner {

    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(title = "信访id")
    private Long petitionId;

    @Schema(title = "信访人id")
    private Long petitionerId;

    private LocalDateTime createTime;

}
