package com.smxz.dakongshen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName("petitioner_portrait")
public class PetitionerPortrait {

    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(title = "信访人名称")
    private String petitionerName;

    @Schema(title = "风险等级")
    private String riskLevel;

    @Schema(title = "标签总结")
    private String tagSummary;

    @Schema(title = "信访历史总结")
    private String petitionHistorySummary;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
