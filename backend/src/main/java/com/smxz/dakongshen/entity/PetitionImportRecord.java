package com.smxz.dakongshen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName("petition_import_record")
public class PetitionImportRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(title = "导入类型")
    private String importType;

    @Schema(title = "导入状态")
    private String importStatus;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
