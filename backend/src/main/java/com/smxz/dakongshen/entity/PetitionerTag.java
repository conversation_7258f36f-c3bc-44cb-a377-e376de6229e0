package com.smxz.dakongshen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName("petitioner_tag")
public class PetitionerTag {

    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(title = "来访人id")
    private Long petitionerId;

    @Schema(title = "标签内容")
    private String content;

    @Schema(title = "标签风险等级")
    private String level;

    private LocalDateTime createTime;
}
