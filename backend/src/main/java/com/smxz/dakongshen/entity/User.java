package com.smxz.dakongshen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 用户实体类
 */
@Data
@TableName("user")
public class User {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 登录id
     */
    private String loginId;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 用户名
     */
    private String name;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 单位id
     */
    private String corp;
} 