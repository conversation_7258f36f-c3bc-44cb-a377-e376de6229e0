package com.smxz.dakongshen.util;

import org.springframework.util.DigestUtils;

/**
 * 密码加密工具类
 */
public class PasswordUtil {

    /**
     * 密码加密盐值
     */
    private static final String SALT = "smxz";

    /**
     * 对密码进行MD5加盐加密
     * @param password 原始密码
     * @return 加密后的密码
     */
    public static String encrypt(String password) {
        // 将密码与盐值拼接后进行MD5加密
        return DigestUtils.md5DigestAsHex((password + SALT).getBytes());
    }

    /**
     * 验证密码是否正确
     * @param password 原始密码
     * @param encryptedPassword 加密后的密码
     * @return 是否匹配
     */
    public static boolean matches(String password, String encryptedPassword) {
        return encrypt(password).equals(encryptedPassword);
    }
} 