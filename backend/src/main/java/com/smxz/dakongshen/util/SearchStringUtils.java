package com.smxz.dakongshen.util;

import com.smxz.commons.exception.BusinessException;
import com.smxz.dakongshen.dto.SearchInfo;

import java.util.List;
import java.util.regex.Pattern;

public class SearchStringUtils {
    private static final List<String> NAME_PREFIX = List.of("姓名", "名称", "名字");

    private static final List<String> ID_PREFIX = List.of("身份证", "身份证号码", "身份证号");

    private static final List<String> CASE_PREFIX = List.of("基本案情", "案情", "情况");

    private static final String CONTENT_SEPARATOR = "[:：]";

    public static SearchInfo analyse(String searchContent) {
        // 先找前三个逗号
        String[] strings = searchContent.split("[,，;；]", 3);
        if (strings.length < 3) {
            throw new BusinessException("输入格式错误！");
        }

        SearchInfo result = new SearchInfo();
        MatchState matchState;

        String strOne = strings[0].trim();
        String[] s = strOne.split(CONTENT_SEPARATOR);
        if (containAny(s[0], NAME_PREFIX)) {
            result.setName(s[1].trim());
            matchState = MatchState.NAME;
        } else if (containAny(s[0], ID_PREFIX)) {
            result.setIdNumber(s[1].trim());
            matchState = MatchState.ID_NUMBER;
        } else {
            // 没匹配上认为是按照 名字，身份证号，案情的顺序
            String temp = s[0].trim();
            result.setName(temp);
            matchState = MatchState.NAME;
        }

        String strTwo = strings[1].trim();
        s = strTwo.split(CONTENT_SEPARATOR);
        if (matchState == MatchState.ID_NUMBER && containAny(s[0], NAME_PREFIX)) {
            result.setName(s[1].trim());
        } else if (matchState == MatchState.NAME && containAny(s[0], ID_PREFIX)) {
            String idNumber = s[1].trim();
            result.setIdNumber(idNumber);
        } else {
            throw new BusinessException("输入格式错误！");
        }

        // 尝试去掉前缀
        String caseInfoString = strings[2].trim();
        s = caseInfoString.split(CONTENT_SEPARATOR, 2);
        if (startWithAny(s[0].trim(), CASE_PREFIX)) {
            result.setCaseInfo(s[1].trim());
        } else {
            result.setCaseInfo(caseInfoString);
        }

        return result;
    }

    public static SearchInfo analyseWithIdValid(String searchContent) {
        // 先找前三个逗号
        String[] strings = searchContent.split("[,，;；]", 3);
        if (strings.length < 3) {
            throw new BusinessException("输入格式错误！");
        }

        SearchInfo result = new SearchInfo();
        MatchState matchState;

        String strOne = strings[0].trim();
        String[] s = strOne.split(CONTENT_SEPARATOR);
        if (containAny(s[0], NAME_PREFIX)) {
            result.setName(s[1].trim());
            matchState = MatchState.NAME;
        } else if (containAny(s[0], ID_PREFIX)) {
            String idNumber = s[1].trim();
            if (!isValidIdCard(idNumber)) {
                throw new BusinessException("身份证输入格式错误！");
            }
            result.setIdNumber(idNumber);
            matchState = MatchState.ID_NUMBER;
        } else {
            // 尝试一下是不是身份证号码
            String temp = s[0].trim();
            if (isValidIdCard(temp)) {
                result.setIdNumber(temp);
                matchState = MatchState.ID_NUMBER;
            } else {
                // 没匹配上认为是按照 名字，身份证号，案情的顺序
                result.setName(temp);
                matchState = MatchState.NAME;
            }
        }

        String strTwo = strings[1].trim();
        s = strTwo.split(CONTENT_SEPARATOR);
        if (matchState == MatchState.ID_NUMBER && containAny(s[0], NAME_PREFIX)) {
            result.setName(s[1].trim());
        } else if (matchState == MatchState.NAME && containAny(s[0], ID_PREFIX)) {
            String idNumber = s[1].trim();
            if (isValidIdCard(idNumber)) {
                result.setIdNumber(idNumber);
            } else {
                throw new BusinessException("身份证输入格式错误！");
            }
        } else if (matchState == MatchState.ID_NUMBER) {
            result.setName(s[0].trim());
        } else {
            String idNumber = s[0].trim();
            if (isValidIdCard(idNumber)) {
                result.setIdNumber(idNumber);
            } else {
                throw new BusinessException("输入格式错误！");
            }
        }

        // 尝试去掉前缀
        String caseInfoString = strings[2].trim();
        s = caseInfoString.split(CONTENT_SEPARATOR, 2);
        if (startWithAny(s[0].trim(), CASE_PREFIX)) {
            result.setCaseInfo(s[1].trim());
        } else {
            result.setCaseInfo(caseInfoString);
        }

        return result;
    }

    private static boolean containAny(String str, List<String> s) {
        for (String e : s) {
            if (str.contains(e)) {
                return true;
            }
        }

        return false;
    }

    private static boolean startWithAny(String str, List<String> s) {
        for (String e : s) {
            if (str.startsWith(e)) {
                return true;
            }
        }
        return false;
    }

    // 校验码对照表（余数 -> 校验字符）
    private static final char[] CHECK_CODES = {'1','0','X','9','8','7','6','5','4','3','2'};
    // 前17位的权重系数
    private static final int[] COEFFICIENTS = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};

    public static boolean isValidIdCard(String idNumber) {
        // 1. 基础校验：非空且长度18
        if (idNumber == null || idNumber.length() != 18) {
            return false;
        }

        // 2. 检查前17位为数字，最后一位是数字或X/x
        if (!Pattern.matches("\\d{17}[\\dXx]", idNumber)) {
            return false;
        }

        // 3. 计算校验码
        int sum = 0;
        try {
            // 遍历前17位字符
            for (int i = 0; i < 17; i++) {
                // 将字符转为数字，乘以权重后累加
                int digit = Character.getNumericValue(idNumber.charAt(i));
                sum += digit * COEFFICIENTS[i];
            }
        } catch (NumberFormatException e) {
            return false; // 出现非数字字符（理论上不会发生）
        }

        // 计算余数并获取正确校验码
        int remainder = sum % 11;
        char correctCheckCode = CHECK_CODES[remainder];

        // 4. 比对校验码（允许x/X）
        char inputCheckCode = Character.toUpperCase(idNumber.charAt(17));
        return correctCheckCode == inputCheckCode;
    }

    private enum MatchState {
        NAME, ID_NUMBER, CASE_INFO;
    }
}
