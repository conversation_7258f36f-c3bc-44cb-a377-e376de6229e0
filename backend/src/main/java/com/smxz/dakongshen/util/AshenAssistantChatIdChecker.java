package com.smxz.dakongshen.util;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.smxz.dakongshen.entity.AshenAssistant;
import com.smxz.dakongshen.mapper.AshenAssistantMapper;
import com.smxz.ragflow.model.BaseResponse;
import com.smxz.ragflow.model.assistant.ChatAssistantCreateRequest;
import com.smxz.ragflow.model.assistant.ChatAssistantData;
import com.smxz.ragflow.service.ChatAssistantService;
import com.smxz.smxzpromptmanagespringbootstarter.entity.PromptConfig;
import com.smxz.smxzpromptmanagespringbootstarter.service.PromptConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Component
public class AshenAssistantChatIdChecker implements ApplicationRunner {

    @Autowired
    private AshenAssistantMapper ashenAssistantMapper;

    @Autowired
    private ChatAssistantService chatAssistantService;

    @Autowired
    private PromptConfigService promptConfigService;

    @Value("${ashen.database-id}")
    private String ashenDataBaseId;

    @Value("${ashen.model-name}")
    private String ashenModelName;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        AshenAssistant assistant = ashenAssistantMapper.selectOne(Wrappers.emptyWrapper(), false);
        if (assistant != null) {
            AshenAssistantChatIdHolder.setChatId(assistant.getChatId());
        } else {
            ChatAssistantCreateRequest assistantCreateRequest = ChatAssistantCreateRequest.builder()
                    .name("阿申")
                    .description("AI问答助手阿申")
                    .datasetIds(List.of(ashenDataBaseId)).build();
            assistantCreateRequest.addLlmSetting("model_name", ashenModelName);
            assistantCreateRequest.addPromptSetting("empty_response", "");
            assistantCreateRequest.addPromptSetting("opener", "");

            // 从库里面获取提示词
            PromptConfig config = promptConfigService.getLastesByCategoryKeyAndKey("ashen", "systemPrompt");
            String prompt;
            // 获取失败
            if (config == null) {
                prompt = DEFAULT_PROMPT;
                log.warn("无法从库表中获取最新提示词，使用默认提示词：{}", prompt);
            } else {
                prompt = config.getContent();
                log.info("使用最新提示词创建阿申，提示词：{}", prompt);
            }
            assistantCreateRequest.addPromptSetting("prompt", prompt);

            BaseResponse<ChatAssistantData> response = chatAssistantService.createAssistant(assistantCreateRequest);
            assistant = new AshenAssistant();
            assistant.setChatId(response.getData().getId());
            assistant.setCreateTime(LocalDateTime.now());
            ashenAssistantMapper.insert(assistant);
            AshenAssistantChatIdHolder.setChatId(assistant.getChatId());
        }
    }

    private static final String DEFAULT_PROMPT = """
            你是一个智能助手，请总结知识库的内容来回答问题，请列举知识库中的数据详细回答。
            以下是知识库：
            {knowledge}
            以上是知识库。
            
            # 绝对禁止
            1.你不是检察官，不能代替检察官做出任何决策、不承诺结果。
            2.你的回答必须严格限定在{knowledge}内。禁止推测、引申、杜撰或添加任何未经知识库记载的信息。
            3.不对未决案件发表评论或预测结果。
            4.不提供主观意见、情感化表达或与信访工作无关的建议。
            5.不回答政治性敏感问题或涉密问题。
            6.不对检察官、法院、其他部门或信访人进行任何形式的负面评价。
            
            # 问答流程
            - 问题分类：识别「程序性/法律适用/信访权利/解释引导」类型
            -问题涉及的业务类型：识别「信访工作/控告检察/刑事申诉/国家赔偿/司法救助/民事行政/公开听证」类型
            - 检索范围：严格限定{knowledge}
            
            # 输出要求
            1.输出格式：回答应按逻辑清晰分块呈现，建议格式如下，也可适问题复杂程度进行调整：
            【建议】：基于{knowledge}的内容输出建议（如安抚情绪、引导依法信访、明确权利义务、下一步工作操作步骤）。
            【风险提示/注意事项】：指出需要检察官特别留意的情况（如程序瑕疵、易激化矛盾的表述）。
            【政策依据/法律条文】：核心依据，明确引用。（例：《信访工作指引人民检察院信访工作规定》第15条 信访人可以采用信息网络、书信、电话、传真、走访等形 式 ，向人民检察院提出控告、申诉、举报，或者建议、意见，人民检察院 应当依法处理。）
            【相关FAQ】（可选）：快速链接到{knowledge}中《百问百答工作手册》相近的问题和回复内容。
            2.语言简洁精准，避免模糊：使用检察官能迅速理解的法言法语和专业术语（但需保证术语定义在{knowledge}中有清晰解释），避免冗长解释和含糊其辞（如“大概”、“可能”、“通常”需有依据）。复杂问题需提供最核心的关键要点。
            
            # 置信度与不确定性处理
            1.明确置信度： 如果{knowledge}中有唯一、清晰、直接对应的答案，必须清晰给出并明确引用。如果问题较为复杂或{knowledge}中存在多种可能性解释，需清晰列出所有合理的法律或政策视角及其依据，并说明适用条件差异。
            2.关键要求：如{knowledge}中存在冲突信息或明显的模糊地带，必须明确告知检察官此不确定性。例如：“关于该问题，知识库中《XX规定》与《YY指导意见》的侧重点略有不同，需结合案件具体情况判断。要点如下：1)... 2)... 建议请示部门负责人或查阅XX文件获取更全面指引。”
            3. 未知问题处理：对于{knowledge}中完全未覆盖的问题，严禁编造答案。标准回复：“依据当前知识库内容，尚未收录可直接回答该问题的具体规定或指引。建议您查阅其他材料或请示上级/相关部门。”\s
            """;
}
