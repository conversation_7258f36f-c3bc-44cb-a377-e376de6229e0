<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.6</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>

	<groupId>com.smxz</groupId>
	<artifactId>dakongshen</artifactId>
	<version>0.1</version>

	<name>dakongshen</name>
	<description>Demo project for Spring Boot</description>

	<properties>
		<java.version>17</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<knife4j.version>4.5.0</knife4j.version>
		<pdfbox.version>3.0.1</pdfbox.version>
		<guava.version>32.1.3-jre</guava.version>
		<druid.version>1.2.24</druid.version>
		<mybatis-plus.version>3.5.12</mybatis-plus.version>
		<easyexcel.version>4.0.3</easyexcel.version>
		<fastjson2.version>2.0.57</fastjson2.version>
		<commons-collections4.version>4.4</commons-collections4.version>
		<commons-io.version>2.18.0</commons-io.version>
		<poi.version>5.4.1</poi.version>
		<minio.version>8.5.7</minio.version>
		<kotlin.version>2.1.21</kotlin.version>
		<jwt.version>0.11.5</jwt.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-webflux</artifactId>
		</dependency>

		<!-- Spring Security 依赖 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>

		<!-- JWT 依赖 -->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-api</artifactId>
			<version>${jwt.version}</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-impl</artifactId>
			<version>${jwt.version}</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-jackson</artifactId>
			<version>${jwt.version}</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-spring-boot3-starter</artifactId>
			<version>${mybatis-plus.version}</version>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-jsqlparser</artifactId>
			<version>${mybatis-plus.version}</version>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>dynamic-datasource-spring-boot-starter</artifactId>
			<version>3.6.1</version>
		</dependency>

		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<version>9.3.0</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-3-starter</artifactId>
			<version>${druid.version}</version>
		</dependency>

		<!-- knife4j -->
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
			<version>${knife4j.version}</version>
		</dependency>
		<!-- knife4j end -->

		<!-- 引入 AI 相关依赖 start -->
		<dependency>
			<groupId>com.smxz</groupId>
			<artifactId>dynamic-ai-model-spring-boot-starter</artifactId>
			<version>1.1.0</version>
		</dependency>
		<!-- 引入 AI 相关依赖 end -->

		<dependency>
			<groupId>com.smxz</groupId>
			<artifactId>commons-spring-boot-starter</artifactId>
			<version>1.0.6</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

	<!--  引入日志相关依赖 start -->
		<dependency>
			<groupId>com.smxz</groupId>
			<artifactId>smxz-logback</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.smxz</groupId>
			<artifactId>smxz-log4jdbc-p6spy</artifactId>
			<version>1.0.0</version>
		</dependency>
		<!--  引入日志相关依赖 end -->


		<!-- poi office 套件-->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>${poi.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>${poi.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-scratchpad</artifactId>
			<version>${poi.version}</version>
		</dependency>
		<dependency>
			<groupId>com.deepoove</groupId>
			<artifactId>poi-tl</artifactId>
			<version>1.12.2</version>
		</dependency>

		<!-- PDFBox 套件 -->
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>${pdfbox.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>fontbox</artifactId>
			<version>${pdfbox.version}</version>
		</dependency>

		<!-- Google Guava -->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>${guava.version}</version>
		</dependency>

		<dependency>
			<groupId>com.dameng</groupId>
			<artifactId>DmJdbcDriver18</artifactId>
			<version>8.1.1.193</version>
		</dependency>

		<!-- Lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>${easyexcel.version}</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba.fastjson2</groupId>
			<artifactId>fastjson2</artifactId>
			<version>${fastjson2.version}</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba.fastjson2</groupId>
			<artifactId>fastjson2-extension-spring6</artifactId>
			<version>${fastjson2.version}</version>
		</dependency>

		<!-- Apache Commons -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
			<version>${commons-collections4.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>${commons-io.version}</version>
		</dependency>

		<!-- SFTP 相关依赖 -->
		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>0.1.55</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
			<version>2.11.1</version>
		</dependency>
		<dependency>
			<groupId>com.pastdev</groupId>
			<artifactId>jsch-extension</artifactId>
			<version>0.1.11</version>
			<exclusions>
				<exclusion>
					<groupId>com.jcraft</groupId>
					<artifactId>jsch</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- SFTP 相关依赖 end-->

		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.6</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.bouncycastle/bcprov-jdk15on -->
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.70</version>
		</dependency>

		<dependency>
			<groupId>com.smxz</groupId>
			<artifactId>smxz-ragflow-spring-boot-starter</artifactId>
			<version>1.2.0</version>
		</dependency>

		<!-- 核心依赖 -->
		<dependency>
			<groupId>net.javacrumbs.shedlock</groupId>
			<artifactId>shedlock-spring</artifactId>
			<version>5.10.0</version>
		</dependency>

		<!-- 根据你使用的锁存储添加以下之一 -->
		<!-- JDBC (数据库) -->
		<dependency>
			<groupId>net.javacrumbs.shedlock</groupId>
			<artifactId>shedlock-provider-jdbc-template</artifactId>
			<version>5.10.0</version>
		</dependency>


		<!-- onlyoffice -->
		<dependency>
			<groupId>com.smxz</groupId>
			<artifactId>smxz-onlyoffice-spring-boot-starter</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<!-- MinIO -->
		<dependency>
			<groupId>io.minio</groupId>
			<artifactId>minio</artifactId>
			<version>${minio.version}</version>
		</dependency>



		<dependency>
			<groupId>com.smxz</groupId>
			<artifactId>smxz-promptmanage-spring-boot-starter</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.jetbrains.kotlin</groupId>
				<artifactId>kotlin-maven-plugin</artifactId>
				<version>${kotlin.version}</version>
				<executions>
					<execution>
						<id>compile</id>
						<phase>process-sources</phase>
						<goals>
							<goal>compile</goal>
						</goals>
						<configuration>
							<sourceDirs>
								<source>src/main/kotlin</source>
								<source>target/generated-sources/annotations</source>
							</sourceDirs>
						</configuration>
					</execution>
					<execution>
						<id>test-compile</id>
						<phase>process-test-sources</phase>
						<goals>
							<goal>test-compile</goal>
						</goals>
						<configuration>
							<sourceDirs>
								<source>src/test/kotlin</source>
								<source>target/generated-sources/annotations</source>
							</sourceDirs>
						</configuration>
					</execution>
				</executions>
				<configuration>
					<jvmTarget>1.8</jvmTarget>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
