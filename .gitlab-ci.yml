variables:
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"
  # 镜像仓库地址
  DOCKER_REGISTRY: "**************:5000"
  # 后端镜像名称
  BACKEND_IMAGE: "${DOCKER_REGISTRY}/dakongshen/backend"
  # 前端镜像名称
  FRONTEND_IMAGE: "${DOCKER_REGISTRY}/dakongshen/frontend"
  # Docker 仓库认证信息
  DOCKER_AUTH_CONFIG: '{"auths":{"**************:5000":{"username":"$HARBOR_USER","password":"$HARBOR_PASSWORD"}}}'

  # SonarQube 扫描
  SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
  GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  SONAR_HOST_URL: "http://**************:9001"
  SONAR_PROJECT_KEY: "jianchayuan_dakongshen_16d160da-ce26-4051-a96f-bdac1fede344"  # 在SonarQube中创建项目时设置的唯一标识
  SONAR_TOKEN: "$SONARQUBE_TOKEN"       # 需要在GitLab的CI/CD变量中设置

# 定义流水线阶段
stages:
  - sonar
  - build
  - package
  - deploy


# 缓存配置
cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - .m2/repository/
    - frontend/node_modules/

# SonarQube代码检查
sonarqube-check:
  stage: sonar
  tags:
    - maven
  cache:
    policy: pull-push
    key: "sonar-cache-$CI_COMMIT_REF_SLUG"
    paths:
      - "${SONAR_USER_HOME}/cache"
      - sonar-scanner/

  script:
    - cd backend  # 先进入包含pom.xml的目录
    - mvn clean install -DskipTests  # 确保先编译
    - mvn sonar:sonar -Dsonar.projectKey=${SONAR_PROJECT_KEY} -Dsonar.projectName="dakongshen" -Dsonar.host.url=${SONAR_HOST_URL} -Dsonar.token=${SONAR_TOKEN} -Dsonar.java.binaries=target/classes -Dsonar.sourceEncoding=UTF-8 -Dsonar.scm.provider=git
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == 'push'  # 所有推送代码都触发
      when: always
  needs: []  # 明确不依赖任何作业


# 后端构建
build-backend:
  stage: build
  tags:
    - maven
  script:
    - cd backend
    - mvn clean package -DskipTests
  artifacts:
    paths:
      - backend/target/*.jar
    expire_in: 1 day
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "v0.1/630计划"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_PIPELINE_SOURCE == "web"

# 前端构建
build-frontend:
  stage: build
  tags:
    - vue
  script:
    - cd frontend
    - npm install
    - npm run build
  artifacts:
    paths:
      - frontend/dist
    expire_in: 1 day
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "v0.1/630计划"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_PIPELINE_SOURCE == "web"


# 构建 ARM 架构镜像并打包为 tar
build-arm-images:
  stage: package
  tags:
    - docker
  image: **************:5000/base/docker:24.0-cli
  services:
    - docker:dind
  before_script:
    - |
      docker login **************:5000 -u admin -p smxz.harbor.smxz
      # 配置多平台支持
      docker run --privileged --rm tonistiigi/binfmt --install all
      # 预拉取ARM架构的基础镜像到本地
      docker pull **************:5000/base/arm/openjdk:17-jdk
      docker pull **************:5000/base/arm/nginx:latest || true
      # 使用默认构建器（支持本地镜像）
      docker buildx use default
      docker buildx ls
  script:
    - |
      # 构建 arm64 平台镜像并保存
      docker buildx build --platform linux/arm64 \
       --build-arg ARCH_PATH="arm/" \
       -t dks-backend-arm64 \
       -f Dockerfiles/backend.Dockerfile \
       --load .
      docker save dks-backend-arm64 > dks-backend-arm64-${CI_COMMIT_SHA}.tar
      
      docker buildx build --platform linux/arm64 \
       --build-arg ARCH_PATH="arm/" \
       -t dks-frontend-arm64 \
       -f Dockerfiles/frontend.Dockerfile \
       --load .
      docker save dks-frontend-arm64 > dks-frontend-arm64-${CI_COMMIT_SHA}.tar
  artifacts:
    paths:
      - dks-backend-arm64-${CI_COMMIT_SHA}.tar
      - dks-frontend-arm64-${CI_COMMIT_SHA}.tar
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "v0.1/630计划"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "web"
      when: manual
  needs: ["build-backend", "build-frontend"]

# 构建并推送后端Docker镜像
deploy-backend:
  stage: deploy
  tags:
    - docker
  image: **************:5000/base/docker:24.0-cli
  services:
    - docker:dind
  before_script:
    - docker login **************:5000 -u admin -p smxz.harbor.smxz
  script:
    - docker build -t ${BACKEND_IMAGE}:${CI_COMMIT_SHA} -f Dockerfiles/backend.Dockerfile .
    - docker tag ${BACKEND_IMAGE}:${CI_COMMIT_SHA} ${BACKEND_IMAGE}:latest
    - docker push ${BACKEND_IMAGE}:${CI_COMMIT_SHA}
    - docker push ${BACKEND_IMAGE}:latest
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "v0.1/630计划"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "web"
      when: manual
  needs: ["build-backend"]

# 构建并推送前端Docker镜像
deploy-frontend:
  stage: deploy
  tags:
    - docker
  image: **************:5000/base/docker:24.0-cli
  services:
    - docker:dind
  before_script:
    - docker login **************:5000 -u admin -p smxz.harbor.smxz
  script:
    - docker build -t ${FRONTEND_IMAGE}:${CI_COMMIT_SHA} -f Dockerfiles/frontend.Dockerfile .
    - docker tag ${FRONTEND_IMAGE}:${CI_COMMIT_SHA} ${FRONTEND_IMAGE}:latest
    - docker push ${FRONTEND_IMAGE}:${CI_COMMIT_SHA}
    - docker push ${FRONTEND_IMAGE}:latest
  rules:
    - if: $CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "v0.1/630计划"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "web"
      when: manual
  needs: ["build-frontend"]

