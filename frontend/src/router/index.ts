import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/risk-management'
  },
  {
    path: '/risk-management',
    name: 'RiskManagement',
    component: () => import('../views/RiskManagement.vue'),
    meta: {
      title: '信访风险源头治理',
      requiresAuth: true
    }
  },
  {
    path: '/risk-identification',
    name: 'RiskIdentification',
    component: () => import('../views/RiskIdentification.vue'),
    meta: {
      title: '信访案件风险识别与防控',
      requiresAuth: true
    }
  },
  {
    path: '/source-identification',
    name: 'SourceIdentification',
    component: () => import('../views/SourceIdentification.vue'),
    meta: {
      title: '信访风险源头识别与防控',
      requiresAuth: true
    }
  },
  {
    path: '/visitor-portrait',
    name: 'VisitorPortrait',
    component: () => import('../views/VisitorPortrait.vue'),
    meta: {
      title: '来访人员画像',
      requiresAuth: true
    }
  },
  {
    path: '/case-detail/:bmsah',
    name: 'CaseDetail',
    component: () => import('../views/CaseDetail.vue'),
    meta: {
      title: '案件详情',
      requiresAuth: true
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: {
      title: '登录'
    }
  },
  {
    path: '/sync-task-manager',
    name: 'SyncTaskManager',
    component: () => import('../views/SyncTaskManager.vue'),
    meta: {
      title: '同步任务管理',
      requiresAuth: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
// router.beforeEach((to, from, next) => {

  
//   // 检查该路由是否需要登录权限
//   if (to.matched.some(record => record.meta.requiresAuth)) {
//     // 检查是否已登录
//     const token = localStorage.getItem('token')
//     if (!token) {
//       // 未登录，跳转到登录页面
//       next({
//         path: '/login',
//         query: { redirect: to.fullPath } // 保存原本要去的页面路径
//       })
//     } else {
//       // 已登录，继续访问
//       next()
//     }
//   } else {
//     // 不需要登录权限的页面，直接访问
//     next()
//   }
// })

export default router 