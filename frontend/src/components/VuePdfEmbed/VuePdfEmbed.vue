<script setup lang="ts">
import { computed, onBeforeUnmount, ref, shallowRef, toRef, watch, onMounted } from 'vue'
import { AnnotationLayer, TextLayer } from 'pdfjs-dist/legacy/build/pdf.mjs'
import { PDFLinkService } from 'pdfjs-dist/web/pdf_viewer.mjs'
import type {
  OnProgressParameters,
  PDFDocumentProxy,
  PDFPageProxy,
  PageViewport,
} from 'pdfjs-dist'

import type { PasswordRequestParams, Source } from './types'
import {
  addPrintStyles,
  createPrintIframe,
  downloadPdf,
  emptyElement,
  releaseChildCanvases,
} from './utils'
import { useVuePdfEmbed } from './composables'

// 定义高亮数据类型
interface Highlight {
  pageNumber: number
  x: number
  y: number
  width: number
  height: number
  text: string
  pageWidth: number
  pageHeight: number
}

// 定义跨页高亮数据类型
interface CrossPageHighlight {
  id: string
  text: string
  startPage: number
  endPage: number
  segments: HighlightSegment[]
  color?: string
  opacity?: number
}

// 定义高亮片段类型
interface HighlightSegment {
  pageNumber: number
  x: number
  y: number
  width: number
  height: number
  text: string
  startIndex: number
  endIndex: number
}

// 全局文本索引项
interface GlobalTextItem {
  pageNumber: number
  element: HTMLElement
  text: string
  globalStartIndex: number
  globalEndIndex: number
  pageStartIndex: number
  pageEndIndex: number
  rect: DOMRect
  container: HTMLElement
}

// 文本匹配结果
interface TextMatch {
  id: string
  text: string
  globalStartIndex: number
  globalEndIndex: number
  startPage: number
  endPage: number
  segments: MatchSegment[]
}

// 匹配片段
interface MatchSegment {
  pageNumber: number
  startIndex: number
  endIndex: number
  text: string
  elements: GlobalTextItem[]
}

const props = withDefaults(
    defineProps<{
      /**
       * 是否启用注释层
       */
      annotationLayer?: boolean
      /**
       * 期望的页面高度
       */
      height?: number
      /**
       * 根元素标识符（由带有页码后缀的页面容器继承）
       */
      id?: string
      /**
       * 注释图标的路径，包括尾部斜杠
       */
      imageResourcesPath?: string
      /**
       * 文档导航服务
       */
      linkService?: PDFLinkService
      /**
       * 要显示的页码
       */
      page?: number
      /**
       * 期望的页面旋转角度
       */
      rotation?: number
      /**
       * 画布大小与文档大小的期望比例
       */
      scale?: number
      /**
       * 要显示的文档源
       */
      source: Source
      /**
       * 是否启用文本层
       */
      textLayer?: boolean
      /**
       * 期望的页面宽度
       */
      width?: number
      /**
       * 是否显示所有页面
       */
      allPages?: boolean
      /**
       * 要显示的高亮数组
       */
      highlights?: Highlight[]
      /**
       * 跨页高亮数组
       */
      crossPageHighlights?: CrossPageHighlight[]
      /**
       * 高亮文本 - 用于前端文字搜索
       */
      highlightText?: string
      /**
       * 相似度匹配阈值 (0-1之间，默认0.8)
       */
      similarityThreshold?: number
    }>(),
    {
      rotation: 0,
      scale: 1.0,  // 增加默认缩放比例
      width: 800,  // 设置默认宽度
      allPages: false,
      highlights: () => [],
      crossPageHighlights: () => [],
      highlightText: '',
      similarityThreshold: 0.8
    }
)

const emit = defineEmits<{
  (e: 'internal-link-clicked', value: number): void
  (e: 'loaded', value: PDFDocumentProxy): void
  (e: 'loading-failed', value: Error): void
  (e: 'password-requested', value: PasswordRequestParams): void
  (e: 'progress', value: OnProgressParameters): void
  (e: 'rendered'): void
  (e: 'rendering-failed', value: Error): void
  (e: 'text-selected', value: { text: string; pageNumber: number; fileId?: number }): void
}>()

const pageNums = shallowRef<number[]>([])
const pageScales = ref<number[]>([])
const root = shallowRef<HTMLDivElement | null>(null)

// 全局文本索引
const globalTextIndex = ref<GlobalTextItem[]>([])
const globalFullText = ref<string>('')
const crossPageMatches = ref<TextMatch[]>([])

let renderingController: { isAborted: boolean; promise: Promise<void> } | null =
    null

const { doc } = useVuePdfEmbed({
  onError: (e) => {
    pageNums.value = []
    emit('loading-failed', e)
  },
  onPasswordRequest({ callback, isWrongPassword }) {
    emit('password-requested', { callback, isWrongPassword })
  },
  onProgress: (progressParams) => {
    emit('progress', progressParams)
  },
  source: toRef(props, 'source'),
})

const linkService = computed(() => {
  if (!doc.value || !props.annotationLayer) {
    return null
  } else if (props.linkService) {
    return props.linkService
  }

  const service = new PDFLinkService()
  service.setDocument(doc.value)
  service.setViewer({
    scrollPageIntoView: ({ pageNumber }: { pageNumber: number }) => {
      emit('internal-link-clicked', pageNumber)
    },
  })
  return service
})

/**
 * 下载PDF文档
 * @param filename - 预定义的文件名
 */
const download = async (filename: string) => {
  if (!doc.value) {
    return
  }

  const data = await doc.value.getData()
  const metadata = await doc.value.getMetadata()
  const suggestedFilename =
      // @ts-expect-error: contentDispositionFilename 未定义类型
      filename ?? metadata.contentDispositionFilename ?? ''
  downloadPdf(data, suggestedFilename)
}

/**
 * 根据props和宽高比返回实际页面宽度和高度的数组
 * @param ratio - 页面宽高比
 */
const getPageDimensions = (ratio: number): [number, number] => {
  let width: number
  let height: number

  if (props.height && !props.width) {
    height = props.height
    width = height / ratio
  } else {
    width = props.width ?? root.value!.clientWidth
    height = width * ratio
  }

  return [width, height]
}

/**
 * 通过浏览器界面打印PDF文档
 * @param dpi - 打印分辨率
 * @param filename - 预定义的文件名
 * @param allPages - 是否忽略page属性并打印所有页面
 */
const print = async (dpi = 300, filename = '', allPages = false) => {
  if (!doc.value) {
    return
  }

  const printUnits = dpi / 72
  const styleUnits = 96 / 72
  let container: HTMLDivElement
  let iframe: HTMLIFrameElement
  let title: string | undefined

  try {
    container = window.document.createElement('div')
    container.style.display = 'none'
    window.document.body.appendChild(container)
    iframe = await createPrintIframe(container)

    const pageNums =
        props.page && !allPages
            ? [props.page]
            : [...Array(doc.value.numPages + 1).keys()].slice(1)

    await Promise.all(
        pageNums.map(async (pageNum, i) => {
          const page = await doc.value!.getPage(pageNum)
          const viewport = page.getViewport({
            scale: 1,
            rotation: 0,
          })

          if (i === 0) {
            const sizeX = (viewport.width * printUnits) / styleUnits
            const sizeY = (viewport.height * printUnits) / styleUnits
            addPrintStyles(iframe, sizeX, sizeY)
          }

          const canvas = window.document.createElement('canvas')
          canvas.width = viewport.width * printUnits
          canvas.height = viewport.height * printUnits
          container.appendChild(canvas)
          const canvasClone = canvas.cloneNode() as HTMLCanvasElement
          iframe.contentWindow!.document.body.appendChild(canvasClone)

          await page.render({
            canvasContext: canvas.getContext('2d')!,
            intent: 'print',
            transform: [printUnits, 0, 0, printUnits, 0, 0],
            viewport,
          }).promise

          canvasClone.getContext('2d')!.drawImage(canvas, 0, 0)
        })
    )

    if (filename) {
      title = window.document.title
      window.document.title = filename
    }

    iframe.contentWindow?.focus()
    iframe.contentWindow?.print()
  } finally {
    if (title) {
      window.document.title = title
    }

    releaseChildCanvases(container!)
    container!.parentNode?.removeChild(container!)
  }
}

/**
 * 将PDF文档渲染为canvas元素和附加层
 */
const render = async () => {
  if (!doc.value || renderingController?.isAborted) {
    return
  }

  try {
    pageNums.value = props.allPages
        ? [...Array(doc.value.numPages + 1).keys()].slice(1)
        : props.page
            ? [props.page]
            : [1]
    pageScales.value = Array(pageNums.value.length).fill(1)

    await Promise.all(
        pageNums.value.map(async (pageNum, i) => {
          const page = await doc.value!.getPage(pageNum)
          if (renderingController?.isAborted) {
            return
          }
          const pageRotation =
              ((props.rotation % 90 === 0 ? props.rotation : 0) + page.rotate) % 360
          const [canvas, div1, div2] = Array.from(
              root.value!.getElementsByClassName('vue-pdf-embed__page')[i].children
          ) as [HTMLCanvasElement, HTMLDivElement, HTMLDivElement]
          const isTransposed = !!((pageRotation / 90) % 2)
          const viewWidth = page.view[2] - page.view[0]
          const viewHeight = page.view[3] - page.view[1]
          const [actualWidth, actualHeight] = getPageDimensions(
              isTransposed ? viewWidth / viewHeight : viewHeight / viewWidth
          )
          const cssWidth = `${Math.floor(actualWidth)}px`
          const cssHeight = `${Math.floor(actualHeight)}px`
          const pageWidth = isTransposed ? viewHeight : viewWidth
          const pageScale = actualWidth / pageWidth
          const viewport = page.getViewport({
            scale: pageScale,
            rotation: pageRotation,
          })

          pageScales.value[i] = pageScale

          canvas.style.display = 'block'
          canvas.style.width = cssWidth
          canvas.style.height = cssHeight

          const renderTasks = [
            renderPage(
                page,
                viewport.clone({
                  scale: viewport.scale * window.devicePixelRatio * props.scale,
                }),
                canvas
            ),
          ]

          if (props.textLayer) {
            renderTasks.push(
                renderPageTextLayer(
                    page,
                    viewport.clone({
                      dontFlip: true,
                    }),
                    div1
                )
            )
          }

          if (props.annotationLayer) {
            renderTasks.push(
                renderPageAnnotationLayer(
                    page,
                    viewport.clone({
                      dontFlip: true,
                    }),
                    div2 || div1
                )
            )
          }

          return Promise.all(renderTasks)
        })
    )

    if (!renderingController?.isAborted) {
      // 渲染完成后构建全局文本索引
      await buildGlobalTextIndex()

      // 如果有高亮文本，执行跨页搜索和高亮
      if (props.highlightText) {
        const matches = performCrossPageSearch(props.highlightText, props.similarityThreshold)
        crossPageMatches.value = matches
        renderCrossPageHighlights(matches)
      }

      // 渲染预定义的跨页高亮
      if (props.crossPageHighlights && props.crossPageHighlights.length > 0) {
        renderPredefinedCrossPageHighlights(props.crossPageHighlights)
      }

      emit('rendered')
    }
  } catch (e) {
    pageNums.value = []
    pageScales.value = []

    if (!renderingController?.isAborted) {
      emit('rendering-failed', e as Error)
    }
  }
}

/**
 * 渲染页面内容
 * @param page - 页面代理
 * @param viewport - 页面视口
 * @param canvas - HTML画布
 */
const renderPage = async (
    page: PDFPageProxy,
    viewport: PageViewport,
    canvas: HTMLCanvasElement
) => {
  canvas.width = viewport.width
  canvas.height = viewport.height
  await page.render({
    canvasContext: canvas.getContext('2d')!,
    viewport,
  }).promise
}

/**
 * 为指定页面渲染注释层
 * @param page - 页面代理
 * @param viewport - 页面视口
 * @param container - HTML容器
 */
const renderPageAnnotationLayer = async (
    page: PDFPageProxy,
    viewport: PageViewport,
    container: HTMLDivElement
) => {
  emptyElement(container)
  new AnnotationLayer({
    accessibilityManager: null,
    annotationCanvasMap: null,
    annotationEditorUIManager: null,
    div: container,
    page,
    structTreeLayer: null,
    viewport,
  }).render({
    annotations: await page.getAnnotations(),
    div: container,
    imageResourcesPath: props.imageResourcesPath,
    linkService: linkService.value!,
    page,
    renderForms: false,
    viewport,
  })
}

/**
 * 为指定页面渲染文本层
 * @param page - 页面代理
 * @param viewport - 页面视口
 * @param container - HTML容器
 */
const renderPageTextLayer = async (
    page: PDFPageProxy,
    viewport: PageViewport,
    container: HTMLElement
) => {
  emptyElement(container)
  new TextLayer({
    container,
    textContentSource: await page.getTextContent(),
    viewport,
  }).render()

  // 添加文字选择事件监听
  addTextSelectionListener(container, page.pageNumber)
}

/**
 * 添加文字选择事件监听
 * @param container - 文本层容器
 * @param pageNumber - 页码
 */
const addTextSelectionListener = (container: HTMLElement, pageNumber: number) => {
  container.addEventListener('mouseup', () => {
    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
      const selectedText = selection.toString().trim()
      if (selectedText.length > 0) {
        emit('text-selected', {
          text: selectedText,
          pageNumber: pageNumber
        })
      }
    }
  })
}

// 旧的单页高亮函数已被跨页高亮功能替代，此处移除以简化代码

/**
 * 转义正则表达式特殊字符
 * @param string - 需要转义的字符串
 */
const escapeRegExp = (string: string): string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

/**
 * 计算两个字符串的相似度（使用编辑距离算法）
 * @param str1 - 字符串1
 * @param str2 - 字符串2
 * @returns 相似度（0-1之间，1表示完全相同）
 */
const calculateSimilarity = (str1: string, str2: string): number => {
  const len1 = str1.length
  const len2 = str2.length

  if (len1 === 0) return len2 === 0 ? 1 : 0
  if (len2 === 0) return 0

  // 创建编辑距离矩阵
  const matrix = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(0))

  // 初始化第一行和第一列
  for (let i = 0; i <= len1; i++) matrix[i][0] = i
  for (let j = 0; j <= len2; j++) matrix[0][j] = j

  // 填充矩阵
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
      matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,      // 删除
          matrix[i][j - 1] + 1,      // 插入
          matrix[i - 1][j - 1] + cost // 替换
      )
    }
  }

  const editDistance = matrix[len1][len2]
  const maxLength = Math.max(len1, len2)
  return 1 - (editDistance / maxLength)
}

/**
 * 使用滑动窗口在文本中查找相似片段
 * @param searchText - 搜索文本
 * @param targetText - 目标文本
 * @param threshold - 相似度阈值（默认0.8）
 * @returns 匹配结果数组
 */
const findSimilarMatches = (searchText: string, targetText: string, threshold: number = 0.8): Array<{
  start: number
  end: number
  text: string
  similarity: number
}> => {
  const normalizedSearch = searchText.replace(/\s+/g, ' ').trim().toLowerCase()
  const normalizedTarget = targetText.toLowerCase()
  const searchLength = normalizedSearch.length
  const matches: Array<{ start: number, end: number, text: string, similarity: number }> = []

  // 如果搜索文本太短，直接返回
  if (searchLength < 3) return matches

  // 滑动窗口搜索
  for (let i = 0; i <= normalizedTarget.length - searchLength; i++) {
    const window = normalizedTarget.substring(i, i + searchLength)
    const similarity = calculateSimilarity(normalizedSearch, window)

    if (similarity >= threshold) {
      // 找到原始文本中的对应位置
      let originalStart = 0
      let originalEnd = 0
      let charCount = 0

      // 计算原始文本中的起始位置
      for (let j = 0; j < targetText.length && charCount < i; j++) {
        if (targetText[j].toLowerCase() === normalizedTarget[charCount]) {
          charCount++
        }
        originalStart = j + 1
      }

      // 计算原始文本中的结束位置
      charCount = 0
      for (let j = 0; j < targetText.length && charCount < i + searchLength; j++) {
        if (targetText[j].toLowerCase() === normalizedTarget[charCount]) {
          charCount++
        }
        originalEnd = j + 1
      }

      matches.push({
        start: originalStart,
        end: originalEnd,
        text: targetText.substring(originalStart, originalEnd),
        similarity
      })
    }
  }

  // 合并重叠的匹配项
  const mergedMatches = []
  for (const match of matches) {
    const overlapping = mergedMatches.find(m =>
        Math.abs(m.start - match.start) < searchLength * 0.3
    )

    if (overlapping) {
      // 保留相似度更高的匹配
      if (match.similarity > overlapping.similarity) {
        const index = mergedMatches.indexOf(overlapping)
        mergedMatches[index] = match
      }
    } else {
      mergedMatches.push(match)
    }
  }

  return mergedMatches.sort((a, b) => b.similarity - a.similarity)
}

/**
 * 构建全局文本索引
 * 收集所有页面的文本信息，构建跨页文本搜索索引
 */
const buildGlobalTextIndex = async () => {
  if (!root.value || pageNums.value.length === 0) {
    return
  }

  console.log('开始构建全局文本索引...')

  const textItems: GlobalTextItem[] = []
  let globalText = ''

  // 遍历所有页面
  for (let i = 0; i < pageNums.value.length; i++) {
    const pageNum = pageNums.value[i]
    const pageContainer = root.value.getElementsByClassName('vue-pdf-embed__page')[i]

    if (!pageContainer) continue

    const textLayer = pageContainer.querySelector('.textLayer') as HTMLElement
    if (!textLayer) continue

    // 等待文本层完全渲染
    await new Promise(resolve => setTimeout(resolve, 100))

    const textElements = textLayer.querySelectorAll('span')
    if (!textElements || textElements.length === 0) continue

    // 将页面内的文本元素按位置排序
    const sortedElements = Array.from(textElements as NodeListOf<HTMLElement>)
    sortedElements.sort((a, b) => {
      const aRect = a.getBoundingClientRect()
      const bRect = b.getBoundingClientRect()
      const yDiff = aRect.top - bRect.top
      if (Math.abs(yDiff) > 5) {
        return yDiff
      }
      return aRect.left - bRect.left
    })

    // 构建页面文本索引
    let pageText = ''
    const pageStartGlobalIndex = globalText.length

    sortedElements.forEach((element, elementIndex) => {
      const text = element.textContent || ''
      const cleanText = text.replace(/\s+/g, ' ')

      const pageStartIndex = pageText.length
      const globalStartIndex = globalText.length

      pageText += cleanText
      globalText += cleanText

      const pageEndIndex = pageText.length
      const globalEndIndex = globalText.length

      textItems.push({
        pageNumber: pageNum,
        element,
        text: cleanText,
        globalStartIndex,
        globalEndIndex,
        pageStartIndex,
        pageEndIndex,
        rect: element.getBoundingClientRect(),
        container: textLayer
      })
    })

    console.log(`页面 ${pageNum} 文本索引构建完成，文本长度: ${pageText.length}`)
  }

  globalTextIndex.value = textItems
  globalFullText.value = globalText

  console.log(`全局文本索引构建完成，总文本长度: ${globalText.length}，文本项数量: ${textItems.length}`)
  console.log('全局文本预览:', globalText.substring(0, 200) + '...')
}

/**
 * 执行跨页文本搜索（支持相似度匹配）
 * @param searchText - 搜索文本
 * @param similarityThreshold - 相似度阈值（默认0.8）
 */
const performCrossPageSearch = (searchText: string, similarityThreshold: number = 0.8): TextMatch[] => {
  if (!searchText.trim() || !globalFullText.value) {
    console.warn('搜索文本为空或全局文本未构建')
    return []
  }

  console.log('开始跨页文本搜索:', searchText)
  console.log('相似度阈值:', similarityThreshold)
  console.log('全局文本长度:', globalFullText.value.length)
  console.log('全局文本项数量:', globalTextIndex.value.length)

  const matches: TextMatch[] = []
  let matchIndex = 0

  // 第一步：尝试精确匹配
  console.log('🎯 步骤1: 精确匹配')
  const normalizedSearchText = searchText.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim()

  // 创建多种搜索模式
  const searchPatterns = [
    normalizedSearchText,                                    // 原始搜索文本
    normalizedSearchText.replace(/\s+/g, '\\s+'),           // 允许多个空白字符
    normalizedSearchText.replace(/\s+/g, '\\s*'),           // 允许可选空白字符
    normalizedSearchText.replace(/\s+/g, '\\s{0,3}'),       // 允许0-3个空白字符
    // 特殊处理：数字和单位之间的空格问题
    normalizedSearchText.replace(/(\d+)\s+([元人月年日])/g, '$1\\s*$2').replace(/([元人月年日])\s+(\d+)/g, '$1\\s*$2'),
    // 更宽松的匹配：允许任意空白字符分布
    normalizedSearchText.split(/\s+/).join('\\s*'),
  ]

  // 尝试每种搜索模式
  for (const pattern of searchPatterns) {
    try {
      const regex = new RegExp(escapeRegExp(pattern).replace(/\\\\s\+/g, '\\s+').replace(/\\\\s\*/g, '\\s*').replace(/\\\\s\{0,3\}/g, '\\s{0,3}'), 'gi')
      let match

      console.log(`尝试搜索模式: ${pattern}`)

      while ((match = regex.exec(globalFullText.value)) !== null) {
        const matchId = `exact-match-${matchIndex++}`
        const globalStartIndex = match.index
        const globalEndIndex = match.index + match[0].length

        console.log(`找到精确匹配: "${match[0]}" 位置: ${globalStartIndex}-${globalEndIndex}`)

        const matchResult = createMatchFromIndices(matchId, globalStartIndex, globalEndIndex, match[0], 1.0)
        if (matchResult) {
          matches.push(matchResult)
        }

        // 防止无限循环
        if (match.index === regex.lastIndex) {
          regex.lastIndex++
        }
      }
    } catch (error) {
      console.error(`搜索模式 "${pattern}" 执行失败:`, error)
    }
  }

  // 第二步：如果精确匹配失败，尝试相似度匹配
  if (matches.length === 0) {
    console.log('🔍 步骤2: 相似度匹配')
    const similarMatches = findSimilarMatches(normalizedSearchText, globalFullText.value, similarityThreshold)

    console.log(`找到 ${similarMatches.length} 个相似匹配`)

    similarMatches.forEach((similarMatch, index) => {
      const matchId = `similarity-match-${matchIndex++}`
      console.log(`相似匹配 ${index + 1}: "${similarMatch.text}" 相似度: ${similarMatch.similarity.toFixed(3)}`)

      const matchResult = createMatchFromIndices(matchId, similarMatch.start, similarMatch.end, similarMatch.text, similarMatch.similarity)
      if (matchResult) {
        matches.push(matchResult)
      }
    })
  }

  console.log(`跨页搜索完成，找到 ${matches.length} 个匹配项`)
  matches.forEach((match, index) => {
    console.log(`匹配项 ${index + 1}:`, {
      id: match.id,
      text: match.text,
      pages: `${match.startPage}-${match.endPage}`,
      segments: match.segments.length,
      position: `${match.globalStartIndex}-${match.globalEndIndex}`,
      similarity: (match as any).similarity || 1.0
    })
  })

  return matches
}

/**
 * 根据全局索引创建匹配结果
 * @param matchId - 匹配ID
 * @param globalStartIndex - 全局起始索引
 * @param globalEndIndex - 全局结束索引
 * @param matchText - 匹配文本
 * @param similarity - 相似度
 */
const createMatchFromIndices = (matchId: string, globalStartIndex: number, globalEndIndex: number, matchText: string, similarity: number): TextMatch | null => {
  // 找到匹配文本涉及的所有文本项
  const involvedItems = globalTextIndex.value.filter(item =>
      item.globalStartIndex < globalEndIndex && item.globalEndIndex > globalStartIndex
  )

  if (involvedItems.length === 0) {
    console.warn('未找到涉及的文本项，跳过此匹配')
    return null
  }

  // 确定起始和结束页面
  const startPage = Math.min(...involvedItems.map(item => item.pageNumber))
  const endPage = Math.max(...involvedItems.map(item => item.pageNumber))

  console.log(`匹配跨页范围: ${startPage} - ${endPage}`)

  // 按页面分组创建匹配片段
  const segmentsByPage = new Map<number, MatchSegment>()

  involvedItems.forEach(item => {
    const itemStartInMatch = Math.max(0, item.globalStartIndex - globalStartIndex)
    const itemEndInMatch = Math.min(matchText.length, item.globalEndIndex - globalStartIndex)

    if (itemStartInMatch < itemEndInMatch) {
      const pageNum = item.pageNumber

      if (!segmentsByPage.has(pageNum)) {
        segmentsByPage.set(pageNum, {
          pageNumber: pageNum,
          startIndex: globalStartIndex,
          endIndex: globalEndIndex,
          text: '',
          elements: []
        })
      }

      const segment = segmentsByPage.get(pageNum)!
      segment.elements.push(item)

      // 计算在当前文本项中的匹配部分
      const matchStartInItem = Math.max(0, globalStartIndex - item.globalStartIndex)
      const matchEndInItem = Math.min(item.text.length, globalEndIndex - item.globalStartIndex)
      const matchedTextInItem = item.text.substring(matchStartInItem, matchEndInItem)

      segment.text += matchedTextInItem

      console.log(`页面 ${pageNum} 匹配片段: "${matchedTextInItem}"`)
    }
  })

  const segments = Array.from(segmentsByPage.values())

  return {
    id: matchId,
    text: matchText,
    globalStartIndex,
    globalEndIndex,
    startPage,
    endPage,
    segments,
    similarity
  } as TextMatch & { similarity: number }
}

/**
 * 渲染跨页高亮
 * @param matches - 文本匹配结果
 */
const renderCrossPageHighlights = (matches: TextMatch[]) => {
  if (!matches || matches.length === 0) {
    return
  }

  console.log('开始渲染跨页高亮...')

  // 清理之前的跨页高亮
  clearCrossPageHighlights()

  matches.forEach((match, matchIndex) => {
    const isFirstMatch = matchIndex === 0
    const similarity = (match as any).similarity || 1.0

    // 根据相似度调整颜色
    let baseColor: string
    let borderColor: string

    if (similarity >= 0.95) {
      // 高相似度：绿色
      baseColor = isFirstMatch ? 'rgba(0, 255, 0, 0.5)' : 'rgba(0, 255, 0, 0.4)'
      borderColor = isFirstMatch ? 'rgba(0, 200, 0, 0.8)' : 'rgba(0, 200, 0, 0.6)'
    } else if (similarity >= 0.8) {
      // 中等相似度：黄色
      baseColor = isFirstMatch ? 'rgba(255, 255, 0, 0.5)' : 'rgba(255, 255, 0, 0.4)'
      borderColor = isFirstMatch ? 'rgba(255, 200, 0, 0.8)' : 'rgba(255, 200, 0, 0.6)'
    } else {
      // 低相似度：橙色
      baseColor = isFirstMatch ? 'rgba(255, 165, 0, 0.5)' : 'rgba(255, 165, 0, 0.4)'
      borderColor = isFirstMatch ? 'rgba(255, 140, 0, 0.8)' : 'rgba(255, 140, 0, 0.6)'
    }

    match.segments.forEach((segment, segmentIndex) => {
      const isFirstSegment = segmentIndex === 0
      const isLastSegment = segmentIndex === match.segments.length - 1

      segment.elements.forEach(item => {
        // 计算在当前元素中的精确匹配位置
        const matchStartInElement = Math.max(0, match.globalStartIndex - item.globalStartIndex)
        const matchEndInElement = Math.min(item.text.length, match.globalEndIndex - item.globalStartIndex)

        if (matchStartInElement < matchEndInElement) {
          const matchedText = item.text.substring(matchStartInElement, matchEndInElement)

          // 创建高亮覆盖层
          const highlightOverlay = createHighlightOverlay(
              item,
              matchStartInElement,
              matchEndInElement,
              matchedText,
              {
                backgroundColor: baseColor,
                borderColor: borderColor,
                isFirstMatch,
                isFirstSegment,
                isLastSegment,
                matchId: match.id,
                segmentIndex,
                similarity
              }
          )

          if (highlightOverlay) {
            item.container.appendChild(highlightOverlay)
          }
        }
      })
    })
  })

  // 滚动到第一个匹配项
  if (matches.length > 0) {
    setTimeout(() => {
      const firstHighlight = root.value?.querySelector('.pdf-cross-page-highlight')
      if (firstHighlight) {
        firstHighlight.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
      }
    }, 100)
  }

  console.log(`跨页高亮渲染完成，共渲染 ${matches.length} 个匹配项`)
}

/**
 * 创建高亮覆盖层
 * @param item - 文本项
 * @param startIndex - 开始索引
 * @param endIndex - 结束索引
 * @param text - 匹配文本
 * @param options - 样式选项
 */
const createHighlightOverlay = (
    item: GlobalTextItem,
    startIndex: number,
    endIndex: number,
    text: string,
    options: {
      backgroundColor: string
      borderColor: string
      isFirstMatch: boolean
      isFirstSegment: boolean
      isLastSegment: boolean
      matchId: string
      segmentIndex: number
      similarity?: number
    }
): HTMLElement | null => {
  try {
    const element = item.element
    const elementText = item.text

    // 获取元素的位置和样式信息
    const rect = element.getBoundingClientRect()
    const containerRect = item.container.getBoundingClientRect()
    const computedStyle = window.getComputedStyle(element)

    // 创建临时元素来测量文本宽度
    const measureElement = document.createElement('span')
    measureElement.style.font = computedStyle.font
    measureElement.style.fontSize = computedStyle.fontSize
    measureElement.style.fontFamily = computedStyle.fontFamily
    measureElement.style.fontWeight = computedStyle.fontWeight
    measureElement.style.letterSpacing = computedStyle.letterSpacing
    measureElement.style.visibility = 'hidden'
    measureElement.style.position = 'absolute'
    measureElement.style.whiteSpace = 'nowrap'
    document.body.appendChild(measureElement)

    // 测量匹配开始位置之前的文本宽度
    const beforeText = elementText.substring(0, startIndex)
    measureElement.textContent = beforeText
    const beforeWidth = measureElement.offsetWidth

    // 测量匹配文本的宽度
    measureElement.textContent = text
    const matchWidth = measureElement.offsetWidth

    // 清理测量元素
    document.body.removeChild(measureElement)

    // 计算高亮区域的精确位置
    const highlightLeft = rect.left - containerRect.left + beforeWidth
    const highlightTop = rect.top - containerRect.top
    const highlightWidth = matchWidth
    const highlightHeight = rect.height

    // 创建高亮覆盖层
    const highlightOverlay = document.createElement('div')
    highlightOverlay.className = 'pdf-cross-page-highlight'
    highlightOverlay.setAttribute('data-match-id', options.matchId)
    highlightOverlay.setAttribute('data-segment-index', options.segmentIndex.toString())
    highlightOverlay.setAttribute('data-page-number', item.pageNumber.toString())

    // 添加相似度信息
    if (options.similarity !== undefined) {
      highlightOverlay.setAttribute('data-similarity', options.similarity.toFixed(3))
      highlightOverlay.title = `相似度: ${(options.similarity * 100).toFixed(1)}%`
    }

    // 设置基本样式
    highlightOverlay.style.position = 'absolute'
    highlightOverlay.style.left = `${highlightLeft}px`
    highlightOverlay.style.top = `${highlightTop}px`
    highlightOverlay.style.width = `${highlightWidth}px`
    highlightOverlay.style.height = `${highlightHeight}px`
    highlightOverlay.style.backgroundColor = options.backgroundColor
    highlightOverlay.style.border = `1px solid ${options.borderColor}`
    highlightOverlay.style.borderRadius = '2px'
    highlightOverlay.style.pointerEvents = 'none'
    highlightOverlay.style.zIndex = '3'
    highlightOverlay.style.transition = 'all 0.2s ease'

    // 为跨页高亮添加特殊样式
    if (options.isFirstMatch) {
      highlightOverlay.style.animation = 'crossPageHighlightFadeIn 0.5s ease-out, highlight-pulse 2s ease-in-out 3'
      highlightOverlay.style.boxShadow = '0 0 8px rgba(255, 235, 59, 0.6)'
    } else {
      highlightOverlay.style.animation = 'crossPageHighlightFadeIn 0.3s ease-out'
    }

    // 为跨页片段添加连接指示
    if (options.isFirstSegment && !options.isLastSegment) {
      // 第一个片段，添加右侧连接指示
      highlightOverlay.style.borderRight = '2px dashed rgba(255, 150, 0, 0.8)'
      highlightOverlay.setAttribute('data-connection', 'start')
    } else if (options.isLastSegment && !options.isFirstSegment) {
      // 最后一个片段，添加左侧连接指示
      highlightOverlay.style.borderLeft = '2px dashed rgba(255, 150, 0, 0.8)'
      highlightOverlay.setAttribute('data-connection', 'end')
    } else if (!options.isFirstSegment && !options.isLastSegment) {
      // 中间片段，添加两侧连接指示
      highlightOverlay.style.borderLeft = '2px dashed rgba(255, 150, 0, 0.8)'
      highlightOverlay.style.borderRight = '2px dashed rgba(255, 150, 0, 0.8)'
      highlightOverlay.setAttribute('data-connection', 'middle')
    }

    return highlightOverlay
  } catch (error) {
    console.error('创建高亮覆盖层失败:', error)
    return null
  }
}

/**
 * 清理跨页高亮
 */
const clearCrossPageHighlights = () => {
  if (!root.value) return

  const crossPageHighlights = root.value.querySelectorAll('.pdf-cross-page-highlight')
  crossPageHighlights.forEach(highlight => {
    highlight.remove()
  })
}

/**
 * 清理所有高亮（包括单页和跨页）
 */
const clearAllHighlights = () => {
  if (!root.value) return

  // 清理单页文字搜索高亮
  const searchHighlights = root.value.querySelectorAll('.pdf-text-search-highlight')
  searchHighlights.forEach(highlight => {
    highlight.remove()
  })

  // 清理跨页高亮
  clearCrossPageHighlights()
}

/**
 * 渲染预定义的跨页高亮
 * @param crossPageHighlights - 跨页高亮数组
 */
const renderPredefinedCrossPageHighlights = (crossPageHighlights: CrossPageHighlight[]) => {
  if (!crossPageHighlights || crossPageHighlights.length === 0) {
    return
  }

  console.log('开始渲染预定义跨页高亮...')

  crossPageHighlights.forEach((highlight, index) => {
    const isFirstHighlight = index === 0
    const baseColor = highlight.color || (isFirstHighlight ? 'rgba(255, 255, 0, 0.5)' : 'rgba(255, 255, 0, 0.4)')
    const opacity = highlight.opacity || (isFirstHighlight ? 0.5 : 0.4)

    highlight.segments.forEach((segment, segmentIndex) => {
      const isFirstSegment = segmentIndex === 0
      const isLastSegment = segmentIndex === highlight.segments.length - 1

      // 找到对应页面的文本层容器
      const pageIndex = pageNums.value.indexOf(segment.pageNumber)
      if (pageIndex === -1) return

      const pageContainer = root.value?.getElementsByClassName('vue-pdf-embed__page')[pageIndex]
      if (!pageContainer) return

      const textLayer = pageContainer.querySelector('.textLayer') as HTMLElement
      if (!textLayer) return

      // 创建高亮覆盖层
      const highlightOverlay = document.createElement('div')
      highlightOverlay.className = 'pdf-cross-page-highlight pdf-predefined-highlight'
      highlightOverlay.setAttribute('data-highlight-id', highlight.id)
      highlightOverlay.setAttribute('data-segment-index', segmentIndex.toString())
      highlightOverlay.setAttribute('data-page-number', segment.pageNumber.toString())

      // 设置样式
      highlightOverlay.style.position = 'absolute'
      highlightOverlay.style.left = `${segment.x}px`
      highlightOverlay.style.top = `${segment.y}px`
      highlightOverlay.style.width = `${segment.width}px`
      highlightOverlay.style.height = `${segment.height}px`
      highlightOverlay.style.backgroundColor = baseColor
      highlightOverlay.style.border = '1px solid rgba(255, 200, 0, 0.6)'
      highlightOverlay.style.borderRadius = '2px'
      highlightOverlay.style.pointerEvents = 'auto'
      highlightOverlay.style.cursor = 'pointer'
      highlightOverlay.style.zIndex = '3'
      highlightOverlay.style.transition = 'all 0.2s ease'

      // 添加连接指示
      if (isFirstSegment && !isLastSegment) {
        highlightOverlay.style.borderRight = '2px dashed rgba(255, 150, 0, 0.8)'
        highlightOverlay.setAttribute('data-connection', 'start')
      } else if (isLastSegment && !isFirstSegment) {
        highlightOverlay.style.borderLeft = '2px dashed rgba(255, 150, 0, 0.8)'
        highlightOverlay.setAttribute('data-connection', 'end')
      } else if (!isFirstSegment && !isLastSegment) {
        highlightOverlay.style.borderLeft = '2px dashed rgba(255, 150, 0, 0.8)'
        highlightOverlay.style.borderRight = '2px dashed rgba(255, 150, 0, 0.8)'
        highlightOverlay.setAttribute('data-connection', 'middle')
      }

      // 添加动画效果
      if (isFirstHighlight) {
        highlightOverlay.style.animation = 'crossPageHighlightFadeIn 0.5s ease-out, highlight-pulse 2s ease-in-out 3'
        highlightOverlay.style.boxShadow = '0 0 8px rgba(255, 235, 59, 0.6)'
      } else {
        highlightOverlay.style.animation = 'crossPageHighlightFadeIn 0.3s ease-out'
      }

      // 添加点击事件
      highlightOverlay.addEventListener('click', () => {
        console.log('跨页高亮被点击:', {
          highlightId: highlight.id,
          segmentIndex,
          pageNumber: segment.pageNumber,
          text: highlight.text
        })
      })

      textLayer.appendChild(highlightOverlay)
    })
  })

  console.log(`预定义跨页高亮渲染完成，共渲染 ${crossPageHighlights.length} 个高亮`)
}

watch(
    doc,
    (newDoc) => {
      if (newDoc) {
        emit('loaded', newDoc)
      }
    },
    { immediate: true }
)

watch(
    () => [
      doc.value,
      props.annotationLayer,
      props.height,
      props.imageResourcesPath,
      props.page,
      props.rotation,
      props.scale,
      props.textLayer,
      props.width,
      props.highlightText,
      props.crossPageHighlights
    ],
    async ([newDoc]) => {
      if (newDoc) {
        if (renderingController) {
          renderingController.isAborted = true
          await renderingController.promise
        }

        releaseChildCanvases(root.value)
        renderingController = {
          isAborted: false,
          promise: render(),
        }

        await renderingController.promise
        renderingController = null
      }
    },
    { immediate: true }
)

// watch(() => props.page, (newPage) => {
//   if (root.value) {
//     const pages = root.value.getElementsByClassName('vue-pdf-embed__page')
//     if (pages && pages[newPage - 1]) {
//       pages[newPage - 1].scrollIntoView({ behavior: 'smooth' })
//     }
//   }
// })

onBeforeUnmount(() => {
  releaseChildCanvases(root.value)
})

onMounted(() => {
  if (root.value) {
    setTimeout(() => {
      const pages = root.value?.getElementsByClassName('vue-pdf-embed__page')
      if (pages) {
        console.log('VuePdfEmbed onMounted called')
        // 使用当前页码进行滚动
        if (props.page && pages[props.page - 1]) {
          pages[props.page - 1].scrollIntoView({ behavior: 'smooth' })
        }
      }
    }, 500)
  }
})

defineExpose({
  doc,
  download,
  print
})
</script>

<template>
  <div :id="id" ref="root" class="vue-pdf-embed">
    <div v-for="(pageNum, i) in pageNums" :key="pageNum">
      <slot name="before-page" :page="pageNum" />

      <div
          :id="id && `${id}-${pageNum}`"
          class="vue-pdf-embed__page"
          :style="{
          '--scale-factor': pageScales[i],
          position: 'relative',
        }"
      >
        <canvas />

        <div v-if="textLayer" class="textLayer" />

        <div v-if="annotationLayer" class="annotationLayer" />
      </div>

      <slot name="after-page" :page="pageNum" />
    </div>
  </div>
</template>

<style lang="scss">
.vue-pdf-embed {
  position: relative;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  overflow-x: hidden;

  &__page {
    position: relative;
    margin: 0 auto 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .textLayer {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    opacity: 0.2;
    line-height: 1.0;
    pointer-events: auto;
    user-select: text;
    mix-blend-mode: multiply;

    > span {
      color: transparent;
      position: absolute;
      white-space: pre;
      cursor: text;
      transform-origin: 0% 0%;
      pointer-events: auto;

      &::selection {
        background-color: rgba(0, 0, 255, 0.8);
      }
    }

    .pdf-highlight {
      position: absolute;
      border: 1px solid rgba(255, 170, 0, 0.8);
      border-radius: 2px;
      pointer-events: auto;
      cursor: pointer;
      z-index: 2;
      mix-blend-mode: multiply;
      box-shadow: 0 0 2px rgba(255, 170, 0, 0.3);
      transition: all 0.2s ease;

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 0 4px rgba(255, 170, 0, 0.5);
        background-color: rgba(255, 255, 0, 0.8) !important;
      }

      &:active {
        transform: scale(0.98);
      }
    }
  }

  .annotationLayer {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    z-index: 3;
  }
}

@keyframes highlight-pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.6;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

@keyframes highlightFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
    background: rgba(255, 255, 0, 0.3);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
    background: rgba(255, 235, 59, 0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    background: linear-gradient(120deg, #fff59d 0%, #ffeb3b 100%);
  }
}

/* 文字搜索高亮样式 */
.textLayer .pdf-text-highlight {
  background: linear-gradient(120deg, #fff59d 0%, #ffeb3b 100%) !important;
  color: #1a1a1a !important;
  padding: 2px 4px !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(255, 235, 59, 0.4) !important;
  border: 1px solid #fdd835 !important;
  font-weight: 500 !important;
  position: relative !important;
  display: inline-block !important;
  animation: highlightFadeIn 0.5s ease-out !important;
  z-index: 10 !important;
  transition: all 0.3s ease !important;

  &:hover {
    background: linear-gradient(120deg, #ffee58 0%, #ffc107 100%) !important;
    transform: scale(1.02) !important;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.6) !important;
  }

  &:first-of-type {
    animation: highlightFadeIn 0.5s ease-out, highlight-pulse 2s ease-in-out 3 !important;
  }
}

/* 新的文字搜索高亮样式 */
.pdf-text-search-highlight {
  position: absolute !important;
  background-color: rgba(255, 255, 0, 0.4) !important;
  border: 1px solid rgba(255, 200, 0, 0.6) !important;
  border-radius: 2px !important;
  pointer-events: none !important;
  z-index: 2 !important;
  transition: all 0.2s ease !important;
  animation: highlightFadeIn 0.3s ease-out !important;

  &:hover {
    background-color: rgba(255, 255, 0, 0.6) !important;
    border-color: rgba(255, 200, 0, 0.8) !important;
  }
}

/* 跨页高亮样式 */
.pdf-cross-page-highlight {
  position: absolute !important;
  background-color: rgba(255, 255, 0, 0.4) !important;
  border: 1px solid rgba(255, 200, 0, 0.6) !important;
  border-radius: 2px !important;
  pointer-events: none !important;
  z-index: 3 !important;
  transition: all 0.2s ease !important;
  animation: crossPageHighlightFadeIn 0.3s ease-out !important;

  &:hover {
    background-color: rgba(255, 255, 0, 0.6) !important;
    border-color: rgba(255, 200, 0, 0.8) !important;
    transform: scale(1.02) !important;
  }

  /* 相似度指示样式 */
  &[data-similarity] {
    &::before {
      content: attr(data-similarity);
      position: absolute;
      top: -18px;
      right: -2px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      font-size: 10px;
      padding: 1px 4px;
      border-radius: 2px;
      opacity: 0;
      transition: opacity 0.2s ease;
      pointer-events: none;
      z-index: 10;
    }

    &:hover::before {
      opacity: 1;
    }
  }

  /* 高相似度样式 (>= 0.95) */
  &[data-similarity^="0.9"]:not([data-similarity^="0.90"]):not([data-similarity^="0.91"]):not([data-similarity^="0.92"]):not([data-similarity^="0.93"]):not([data-similarity^="0.94"]) {
    background-color: rgba(0, 255, 0, 0.4) !important;
    border-color: rgba(0, 200, 0, 0.6) !important;

    &:hover {
      background-color: rgba(0, 255, 0, 0.6) !important;
      border-color: rgba(0, 200, 0, 0.8) !important;
    }
  }

  /* 中等相似度样式 (0.8-0.94) */
  &[data-similarity^="0.8"], &[data-similarity^="0.90"], &[data-similarity^="0.91"], &[data-similarity^="0.92"], &[data-similarity^="0.93"], &[data-similarity^="0.94"] {
    background-color: rgba(255, 255, 0, 0.4) !important;
    border-color: rgba(255, 200, 0, 0.6) !important;

    &:hover {
      background-color: rgba(255, 255, 0, 0.6) !important;
      border-color: rgba(255, 200, 0, 0.8) !important;
    }
  }

  /* 低相似度样式 (< 0.8) */
  &[data-similarity^="0.5"], &[data-similarity^="0.6"], &[data-similarity^="0.7"] {
    background-color: rgba(255, 165, 0, 0.4) !important;
    border-color: rgba(255, 140, 0, 0.6) !important;

    &:hover {
      background-color: rgba(255, 165, 0, 0.6) !important;
      border-color: rgba(255, 140, 0, 0.8) !important;
    }
  }

  /* 连接指示样式 */
  &[data-connection="start"] {
    border-right: 2px dashed rgba(255, 150, 0, 0.8) !important;

    &::after {
      content: '→';
      position: absolute;
      right: -8px;
      top: 50%;
      transform: translateY(-50%);
      color: rgba(255, 150, 0, 0.8);
      font-size: 12px;
      font-weight: bold;
    }
  }

  &[data-connection="end"] {
    border-left: 2px dashed rgba(255, 150, 0, 0.8) !important;

    &::before {
      content: '←';
      position: absolute;
      left: -8px;
      top: 50%;
      transform: translateY(-50%);
      color: rgba(255, 150, 0, 0.8);
      font-size: 12px;
      font-weight: bold;
    }
  }

  &[data-connection="middle"] {
    border-left: 2px dashed rgba(255, 150, 0, 0.8) !important;
    border-right: 2px dashed rgba(255, 150, 0, 0.8) !important;

    &::before {
      content: '←';
      position: absolute;
      left: -8px;
      top: 50%;
      transform: translateY(-50%);
      color: rgba(255, 150, 0, 0.8);
      font-size: 12px;
      font-weight: bold;
    }

    &::after {
      content: '→';
      position: absolute;
      right: -8px;
      top: 50%;
      transform: translateY(-50%);
      color: rgba(255, 150, 0, 0.8);
      font-size: 12px;
      font-weight: bold;
    }
  }
}

/* 预定义跨页高亮样式 */
.pdf-predefined-highlight {
  pointer-events: auto !important;
  cursor: pointer !important;

  &:hover {
    background-color: rgba(255, 255, 0, 0.7) !important;
    border-color: rgba(255, 200, 0, 0.9) !important;
    transform: scale(1.05) !important;
    box-shadow: 0 2px 8px rgba(255, 235, 59, 0.4) !important;
  }

  &:active {
    transform: scale(0.98) !important;
  }
}

/* 跨页高亮动画 */
@keyframes crossPageHighlightFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.95);
    background-color: rgba(255, 255, 0, 0.2);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
    background-color: rgba(255, 235, 59, 0.6);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    background-color: rgba(255, 255, 0, 0.4);
  }
}
</style>

<style scoped>
.pdf-viewer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.pdf-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.pdf-embed {
  width: 100%;
  height: 100%;
}

.pdf-highlight-container {
  position: absolute;
  background-color: rgba(255, 255, 0, 0.3);
  pointer-events: none;
  z-index: 1;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.pdf-highlight-container:hover {
  background-color: rgba(255, 255, 0, 0.5);
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #f56c6c;
  font-size: 14px;
  gap: 8px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 2;
}

.loading-icon {
  animation: rotate 1s linear infinite;
  margin-right: 8px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
