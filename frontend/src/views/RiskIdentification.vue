<template>
  <div class="case-risk-page">
    <div class="page-container" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <!-- 侧边栏 -->
      <SideBar 
        :parentKey="'risk-identification'"
        :parentTitle="'风险案件推送'" 
        :items="sidebarMenuItems"
        :defaultActive="'risk-identification'"
        @select="handleMenuSelect"
        @collapse-change="handleSidebarCollapse"
      />
      
      <!-- 主内容区 -->
      <div class="main-container">
        <div class="robot-section">
          <div class="robot-avatar">
            <img src="@/assets/images/robot.gif" width="160" height="180" alt="robot" class="robot-avatar-img">
          </div>
          <div class="robot-greeting">
            <div class="robot-hi">hi~我是阿申</div>
            <div class="robot-desc">我可以快速帮你识别来访人风险，快来试试吧！</div>
          </div>
        </div>
        <SearchBox
          v-model="inputText"
          placeholder="来访人姓名：张三，身份证号码：XXXXXX，现在来到广州市检察院申请民事监督..."
          @submit="onSubmit"
        />
        <!-- 暂时隐藏 -->
        <div class="bubble-arc-bg" v-if="showHistory">
          <div class="bubble-section">
            <div class="bubble-row">
              <div class="bubble" v-for="(item, idx) in bubbles.slice(0, 2)" :key="'row1-' + idx" @click="handleBubbleClick(item.searchContent)">
                <div>{{ item.searchContent }}</div>
              </div>
            </div>
            <div class="bubble-row">
              <div class="bubble" v-for="(item, idx) in bubbles.slice(2, 5)" :key="'row2-' + idx" @click="handleBubbleClick(item.searchContent)">
                <div>{{ item.searchContent }}</div>
              </div>
            </div>
          </div>
        </div>
        <!-- AI助手对话框 -->
        <AIAssistantDialog v-model="showAIDialog" />
        <!-- 悬浮AI助手图标 -->
        <AIAssistantFloat @click="handleAIAssistantClick" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getHistory, submitIdentification, HistoryItem } from '@/api/caseRiskIdentification'
import { ElMessage } from 'element-plus'
import AIAssistantDialog from '@/components/CaseRisk/AIAssistantDialog.vue'
import { useVisitorStore } from '@/stores/visitorStore'
import SearchBox from '@/components/CaseRisk/SearchBox.vue'
import AIAssistantFloat from '@/components/CaseRisk/AIAssistantFloat.vue'
import SideBar from '@/components/SideBar.vue'

const router = useRouter()
const visitorStore = useVisitorStore()
const inputText = ref('')
const bubbles = ref<HistoryItem[]>([])
const isSubmitting = ref(false)
const showAIDialog = ref(false)
const showHistory = ref(import.meta.env.DEV)

// 菜单图标路径
const menuDpIcon = new URL('../assets/icon/menu-dp.svg', import.meta.url).href
const menuSbIcon = new URL('../assets/icon/menu-sb.svg', import.meta.url).href
const menuTsIcon = new URL('../assets/icon/menu-ts.svg', import.meta.url).href

// 侧边栏状态
const sidebarCollapsed = ref(false)
const currentSideMenuItem = ref('risk-identification')

// 侧边栏菜单项
const sidebarMenuItems = [
  {
    key: 'risk-identification',
    title: '风险识别',
    component: 'RiskIdentification',
    icon: menuSbIcon
  }
]

// 处理侧边栏折叠状态变化
const handleSidebarCollapse = (collapsed: boolean) => {
  sidebarCollapsed.value = collapsed
}

// 处理菜单项选择
const handleMenuSelect = (item: any) => {
  currentSideMenuItem.value = item.key
  console.log('选中菜单项:', item)
  // 这里可以根据选中的菜单项加载不同的组件或数据
}

// 处理气泡点击事件
const handleBubbleClick = (content: string) => {
  inputText.value = content
}

const handleAIAssistantClick = () => {
  showAIDialog.value = true
}

// 获取历史记录
const fetchHistory = async () => {
  const historyData = await getHistory()
  bubbles.value = historyData
  console.log(bubbles.value)
}

onMounted(() => {
  fetchHistory()
})

async function onSubmit() {
  if (inputText.value.trim() && !isSubmitting.value) {
    try {
      isSubmitting.value = true
      const response = await submitIdentification(inputText.value)
      
      if (response.data) {
        // 添加新的搜索记录到气泡列表
        bubbles.value.unshift({ searchContent: inputText.value })
        // 只保留最新5条
        if (bubbles.value.length > 5) {
          bubbles.value.length = 5
        }
        // 使用Pinia存储数据并跳转
        visitorStore.setVisitorData({
          ...response.data,
          inputText: inputText.value
        })
        inputText.value = ''
        router.push('/visitor-portrait')
      } else {
        ElMessage.error(response.message || '识别失败，请稍后重试')
      }
    } catch (error) {
      ElMessage.error('系统错误，请稍后重试')
    } finally {
      isSubmitting.value = false
    }
  }
}
</script>

<style scoped>
.case-risk-page {
  width: 100%;
  height: calc(100vh - 70px);
  background: rgb(248, 253, 255);
  padding: 0;
  overflow: hidden;
  position: relative;
}

.page-container {
  display: flex;
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
}

.main-container {
  flex: 1;
  overflow: auto;
  transition: all 0.3s ease;
  position: relative;
}

/* 侧边栏折叠状态下的主容器样式 */
.sidebar-collapsed .main-container {
  margin-left: 35px;
}

.main-title {
  text-align: center;
  margin-top: 30px;
  margin-bottom: 10px;
}
.main-title-text {
  font-size: 32px;
  font-weight: bold;
  color: #2196f3;
}
.main-title-decorator {
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #2196f3 0%, #90caf9 100%);
  margin: 10px auto 0 auto;
  border-radius: 2px;
}
.robot-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 150px;
}
.robot-hi {
  font-size: 30px;
  color: #2196f3;
  font-weight: bold;
  text-align: center;
}
.robot-desc {
  font-size: 35px;
  color: #2196f3;
  font-weight: bold;
  margin-top: 10px;
  text-align: center;
}
.input-section {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}
.input-box {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 30px;
  box-shadow: 0 2px 8px rgba(33,150,243,0.08);
  padding: 0 10px;
  width: 900px;
  max-width: 90vw;
  height: 60px;
}
.input-box input {
  border: none;
  outline: none;
  font-size: 18px;
  flex: 1;
  padding: 16px 12px;
  background: transparent;
}
.input-box button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0 8px;
  display: flex;
  align-items: center;
}
.bubble-arc-bg {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  min-height: 350px;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  flex-shrink: 0;
}
.bubble-arc-bg::before {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 100%;
  height: 400px;
  background: linear-gradient(0deg, #e3f2fd 80%, #f7fbff 100%);
  border-top-left-radius: 50% 100%;
  border-top-right-radius: 50% 100%;
  z-index: 0;
  pointer-events: none;
}
.bubble-section {
  position: relative;
  z-index: 1;
  margin-bottom: 100px;
}
.bubble {
  background: #fff;
  color: #1976d2;
  border-radius: 20px;
  padding: 12px 20px;
  margin: 8px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(33,150,243,0.08);
  transition: all 0.3s ease;
  max-width: 450px;
  overflow: hidden;
}

.bubble:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33,150,243,0.12);
}

.bubble > div {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 500px;
}

.bubble-content {
  color: #1565c0;
  font-size: 15px;
  margin-top: 2px;
}
.bubble-row {
  display: flex;
  justify-content: center;
  gap: 128px;
  margin-bottom: 64px;
}
.bubble-row:last-child {
  margin-bottom: 0;
}
</style> 