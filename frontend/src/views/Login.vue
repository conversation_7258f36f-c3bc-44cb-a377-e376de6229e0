<template>
  <div class="login-bg">
    <div class="login-title">信访案件风险识别与防控</div>
    <div class="login-card">
      <div class="login-form">
        <div class="login-form-title">登录</div>
        
        <!-- 错误消息区域 -->
        <div v-if="errorMsg" class="login-error">
          <el-icon><warning /></el-icon>
          {{ errorMsg }}
        </div>
        
        <el-input 
          v-model="username" 
          placeholder="账号" 
          class="login-input"
          autocomplete="off"
          @keydown.enter="focusPassword"
          ref="usernameInput"
        />
        <el-input 
          v-model="password" 
          placeholder="密码" 
          type="password" 
          class="login-input"
          autocomplete="off"
          @keydown.enter="handleLogin"
          ref="passwordInput"
        />
        <el-button 
          class="login-btn" 
          type="primary" 
          @click.prevent="handleLogin"
          :loading="loading"
        >登录</el-button>
      </div>
      <div class="login-illustration">
        <svg width="160" height="160" viewBox="0 0 160 160" fill="none" xmlns="http://www.w3.org/2000/svg">
          <ellipse cx="80" cy="140" rx="60" ry="15" fill="#E3F0FF"/>
          <g>
            <rect x="50" y="60" width="60" height="50" rx="10" fill="#B3D8FF"/>
            <rect x="60" y="70" width="40" height="30" rx="6" fill="#E3F0FF"/>
            <path d="M70 85l10 10 15-15" stroke="#409EFF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
          </g>
          <ellipse cx="80" cy="140" rx="60" ry="15" fill="#E3F0FF"/>
        </svg>
      </div>
    </div>
    <div class="login-copyright">@广州市人民检察院版权所有</div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElInput, ElButton, ElMessage } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'
import { login } from '../api/auth'
import { useUserStore } from '../stores/userStore'

const router = useRouter()
const userStore = useUserStore()
const username = ref('')
const password = ref('')
const loading = ref(false)
const errorMsg = ref('')

// 添加输入框引用
const usernameInput = ref<InstanceType<typeof ElInput> | null>(null)
const passwordInput = ref<InstanceType<typeof ElInput> | null>(null)

// 聚焦密码输入框
const focusPassword = () => {
  if (passwordInput.value) {
    // 使用$el获取DOM元素，然后找到input子元素并聚焦
    const inputEl = passwordInput.value.$el.querySelector('input')
    if (inputEl) {
      inputEl.focus()
    }
  }
}

// 清除错误消息
const clearError = () => {
  errorMsg.value = ''
}

// 设置错误消息
const setError = (message: string) => {
  errorMsg.value = message
  // 显示全局提示，但不会自动消失
  ElMessage({
    message,
    type: 'error',
    duration: 5000 // 延长显示时间到5秒
  })
}

async function handleLogin(e?: Event) {
  // 如果传入了事件对象，阻止默认行为
  if (e) {
    e.preventDefault()
  }
  
  // 清除之前的错误
  clearError()
  
  if (!username.value || !password.value) {
    setError('请输入账号和密码')
    return
  }
  
  try {
    loading.value = true
    const response = await login(username.value, password.value)
    
    // 验证响应数据是否有效
    if (!response || !response.data) {
      setError('登录失败，服务器响应异常')
      return
    }
    
    const { token, name, loginId } = response.data
    
    // 验证必要字段是否存在
    if (!token || !name || !loginId) {
      setError('登录失败，用户信息不完整')
      return
    }
    
    // 数据有效，保存用户信息
    userStore.setUserInfo({ token, name, loginId })
    
    ElMessage.success('登录成功')
    
    // 检查是否有重定向参数
    const redirectPath = router.currentRoute.value.query.redirect as string
    if (redirectPath) {
      // 有重定向参数，跳转到指定页面
      router.push(redirectPath)
    } else {
      // 没有重定向参数，跳转到默认页面
      router.push('/source-identification')
    }
  } catch (error: any) {
    console.error('登录错误:', error)
    
    // 详细的错误处理
    if (error.response) {
      // 服务器响应了错误状态码
      const status = error.response.status
      if (status === 401) {
        setError('账号或密码错误')
      } else if (status === 500) {
        setError('服务器内部错误，请联系管理员')
      } else if (error.response.data && error.response.data.message) {
        setError(error.response.data.message)
      } else {
        setError(`登录失败 (${status})`)
      }
    } else if (error.request) {
      // 请求发送了但没有收到响应
      setError('无法连接到服务器，请检查网络连接')
    } else {
      // 请求设置时发生错误
      setError('登录请求失败，请稍后再试')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-bg {
  min-height: 100vh;
  background: #54adfa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.login-title {
  color: #fff;
  font-size: 56px;
  font-weight: 600;
  margin-bottom: 60px;
  margin-top: 10px;
  letter-spacing: 2px;
}
.login-card {
  background: #fff;
  border-radius: 40px;
  box-shadow: 0 12px 48px rgba(0,0,0,0.10);
  display: flex;
  align-items: center;
  padding: 72px 100px 72px 80px;
  min-width: 900px;
  min-height: 420px;
  margin-bottom: 60px;
}
.login-form {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  min-width: 340px;
  margin-right: 80px;
}
.login-form-title {
  color: #2196f3;
  font-size: 38px;
  font-weight: 600;
  margin-bottom: 48px;
  text-align: center;
}
.login-error {
  background-color: #fef0f0;
  color: #f56c6c;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  font-size: 16px;
  line-height: 1.5;
}
.login-error .el-icon {
  margin-right: 8px;
  font-size: 18px;
}
.login-input {
  margin-bottom: 36px;
  height: 56px;
  font-size: 22px;
}
.login-btn {
  width: 100%;
  height: 56px;
  background: linear-gradient(90deg, #54adfa 0%, #256dff 100%);
  border: none;
  font-size: 24px;
  font-weight: 600;
}
.login-illustration {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 260px;
}
.login-illustration svg {
  width: 240px;
  height: 240px;
}
.login-copyright {
  color: #fff;
  font-size: 24px;
  text-align: center;
  margin-bottom: 48px;
}
</style> 