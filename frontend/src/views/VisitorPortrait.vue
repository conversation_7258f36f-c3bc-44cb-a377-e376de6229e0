<template>
  <div class="visitor-portrait-page">
    <!-- 顶部输入框 -->
    <SearchBox
      v-model="inputText"
      placeholder="来访人姓名：张三，身份证号码：XXXXXX，现在来到广州市检察院申请民事监督..."
      @submit="onSubmit"
    />
    <!-- 信息区 -->
    <div class="info-area-border">
      <div class="info-area">
      <!-- 左侧：来访人员信息 -->
      <div class="visitor-info">
        <div class="avatar-box">
          <img src="@/assets/images/defaultProfile.jpg" alt="avatar" class="avatar-img" />
        </div>
        <div :class="['risk-level', visitorData.riskLevel]">
          <img src="@/assets/icon/gaojing.svg" class="risk-icon" :alt="getRiskText(visitorData.riskLevel)" />
          <span class="risk-text">{{ getRiskText(visitorData.riskLevel) }}</span>
        </div>
        <table class="info-table">
          <tbody>
            <tr>
              <td width="140px">信访人姓名：</td>
              <td>{{ visitorData.name }}</td>
            </tr>
            <tr>
              <td>性别：</td>
              <td>{{ visitorData.sex }}</td>
            </tr>
            <tr>
              <td>证件号码：</td>
              <td>{{ visitorData.idNumber }}</td>
            </tr>
            <tr>
              <td>联系住址：</td>
              <td>{{ visitorData.contactAddress }}</td>
            </tr>
            <tr>
              <td>户籍所在地：</td>
              <td>{{ visitorData.registeredResidence }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- 右侧：画像与标签 -->
      <div class="portrait-area">
        <!-- 下载报告按钮 -->
        <el-button type="primary" class="download-report-btn" @click="handleDownloadReport">
          <el-icon><Download /></el-icon>
          下载报告
        </el-button>
        <div class="person-img-div">
          <!-- 左侧标签 -->
          <div class="tag-list left-tag-list">
            <span v-for="tag in leftTags" :key="tag.content" :style="tag.css"
                  :class="['tag', getTagClass(tag.riskLevel)]">
              {{ tag.content }}
            </span>
          </div>
          <img src="@/assets/images/human.svg" alt="avatar" class="person-img" />
          <!-- 右侧标签 -->
          <div class="tag-list right-tag-list">
            <span v-for="tag in rightTags" :key="tag.content" 
                  :class="['tag', getTagClass(tag.riskLevel)]" :style="tag.css">
              {{ tag.content }}
            </span>
          </div>
        </div>
        <div class="summary-box">
          <span class="summary-text">
            {{ summaryText }}
          </span>
        </div>
      </div>
    </div>
    </div>

    <!-- 底部tab贴补页 -->
    <div class="tab-section">
      <div class="tab-header-list">
        <div class="tab-header" :class="{ active: activeTab === 'history' }" @click="activeTab = 'history'">历史来访记录</div>
        <div class="tab-header" :class="{ active: activeTab === 'case' }" @click="activeTab = 'case'">类案推荐（{{ caseList.length }}）</div>
        <div class="tab-header disabled" :class="{ active: activeTab === 'family' }">家庭情况</div>
        <div class="tab-header disabled" :class="{ active: activeTab === 'other' }">其它</div>
      </div>
      <div class="tab-content">
        <div v-if="activeTab === 'case'">
          <div v-for="(item, idx) in caseList" :key="idx" class="case-block">
            <el-tooltip content="查看详情" placement="top" :show-after="500">
              <div class="case-header" @click="openCaseDialog(item, idx)">
                <span v-if="item.source" class="case-source-tag">{{ item.source }}</span>
                <span class="case-title">{{ item.title || '案件名称' }}</span>
              </div>
            </el-tooltip>
            <div class="case-content-area">
                              <div class="case-left">
                  <div class="case-tab-list">
                    <div v-for="tab in item.summaries" :key="tab.key"
                      :class="['case-tab', { active: caseTabs[idx] === tab.key }]" @click="caseTabs[idx] = tab.key">
                      <div class="case-tab-content">{{ tab.key }}</div>
                    </div>
                  </div>
                </div>
              <div class="case-right">
                <div class="case-content">
                  <template v-for="summary in item.summaries" :key="summary.key">
                    <template v-if="caseTabs[idx] === summary.key">
                      <div v-html="convertEmToStrong(summary.value)"></div>
                    </template>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else-if="activeTab === 'history'">
          <div class="history-summary">
            <span>{{ visitHistoryRecords?.summary }}</span>
          </div>
          <template v-if="visitHistoryRecords?.visitRecords?.length">
            <el-timeline>
              <el-timeline-item v-for="(item, idx) in visitHistoryRecords?.visitRecords" :key="item.visitTime" :timestamp="item.visitTime"
                placement="top" :type="idx === 0 ? 'primary' : ''" :hollow="idx !== 0">
                <el-card class="timeline-card">
                  <div class="card-header">
                    <span class="case-name">{{ item.caseName }}</span>
                    <el-tag size="small" type="primary" class="timeline-type-tag">{{ item.caseType }}</el-tag>
                  </div>
                  <div class="card-row"><b>信访渠道：</b>{{ item.petitionChannel }}</div>
                  <div class="card-row"><b>案情摘要：</b>{{ item.brief }}</div>
                  <div class="card-row"><b>回复内容：</b>{{ item.replyContent }}</div>
                  <div class="card-row"><b>承办单位：</b>{{ item.organizer }}</div>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </template>
          <div v-else class="empty-history">
            <img src="@/assets/images/暂无数据.svg" alt="暂无数据" class="empty-img" />
            <div class="empty-text">暂无历史来访记录</div>
          </div>
        </div>
        <div v-else-if="activeTab === 'family'">开发中...</div>
        <div v-else>开发中...</div>
      </div>
    </div>
    <!-- 类案详情弹出框 -->
    <el-dialog
      v-model="showCaseDialog"
      :title="caseDetailData?.title || selectedCase?.title || '案件详情'"
      width="1200px"
      :before-close="closeCaseDialog"
      class="case-detail-dialog"
      :show-close="false"
    >
      <div class="case-detail-container" v-if="selectedCase">
        <div v-if="isLoadingCaseDetail" class="case-detail-loading">
          <el-icon class="is-loading" :size="24"><Loading /></el-icon>
          <span>加载中...</span>
        </div>
        <div v-else-if="caseDetailData" class="case-detail-content">
          <div class="case-detail-left">
            <div class="case-detail-tab-list">
              <div v-for="paragraph in caseDetailData.paragraph" :key="paragraph.key"
                :class="['case-detail-tab', { active: selectedCaseTab === paragraph.key }]" 
                @click="selectedCaseTab = paragraph.key">
                <div class="case-detail-tab-content">{{ paragraph.key }}</div>
              </div>
            </div>
          </div>
          <div class="case-detail-right">
            <div class="case-detail-text">
              <template v-for="paragraph in caseDetailData.paragraph" :key="paragraph.key">
                <template v-if="selectedCaseTab === paragraph.key">
                  <div v-html="convertEmToStrong(paragraph.value)"></div>
                </template>
              </template>
            </div>
          </div>
        </div>
        <div v-else class="case-detail-error">
          <span>加载失败，请重试</span>
        </div>
      </div>
    </el-dialog>
    <!-- AI助手对话框 -->
    <AIAssistantDialog v-model="showAIDialog" />
    <!-- 悬浮AI助手图标 -->
    <AIAssistantFloat @click="handleAIAssistantClick" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { PersonInfo, getSummary, TagItem, getRecommendedCases, RecommendedCase, getHistoricalVisitRecords, HistoricalVisitRecords, submitIdentification, getCaseDetail, GetCaseDetailRes, downloadReport } from '@/api/caseRiskIdentification'
import AIAssistantDialog from '@/components/CaseRisk/AIAssistantDialog.vue'
import { useVisitorStore } from '@/stores/visitorStore'
import SearchBox from '@/components/CaseRisk/SearchBox.vue'
import AIAssistantFloat from '@/components/CaseRisk/AIAssistantFloat.vue'
import { ElMessage } from 'element-plus'
import { Loading, Download } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const visitorStore = useVisitorStore()
const visitorData = ref<PersonInfo>({
  name: '',
  idNumber: '',
  brief: '',
  riskLevel: 'low',
  sex: '',  
  registeredResidence: '',
  contactAddress: ''
})

const leftTags = ref<TagItem[]>([])
const rightTags = ref<TagItem[]>([])
const summaryText = ref('')

const visitHistoryRecords = ref<HistoricalVisitRecords>()

const showAIDialog = ref(false)
const aiInput = ref('')
function sendAIMessage() {
  // 这里可以扩展AI消息发送逻辑
  aiInput.value = ''
}

const handleAIAssistantClick = () => {
  showAIDialog.value = true
}

const handleDownloadReport = () => {
  if (!visitorData.value.name || !visitorData.value.idNumber) {
    ElMessage.error('访客信息不完整，无法下载报告')
    return
  }
  
  downloadReport(visitorData.value.name, visitorData.value.idNumber)
}

const isSubmitting = ref(false)

async function onSubmit() {
  if (inputText.value.trim() && !isSubmitting.value) {
    try {
      isSubmitting.value = true
      const response = await submitIdentification(inputText.value)
      if (response.data) {
        // 更新访客数据
        visitorData.value = response.data
        
        // 获取标签和摘要数据
        const summaryData = await getSummary(response.data.idNumber, response.data.name)
        leftTags.value = []
        rightTags.value = []
        let isLeft = true
        for (const tag of summaryData.tags) {
          if (isLeft) {
            leftTags.value.push(tag)
          } else {
            rightTags.value.push(tag)
          }
          isLeft = !isLeft
        }
        summaryText.value = summaryData.summary

        // 获取推荐案例
        const recommendedCases = await getRecommendedCases(response.data.brief)
        caseList.value = recommendedCases
        caseTabs.value = recommendedCases.map(caseItem => caseItem.summaries[0]?.key || '基本案情')

        // 获取历史来访记录
        const historicalRecords = await getHistoricalVisitRecords(response.data.idNumber, response.data.name)
        visitHistoryRecords.value = historicalRecords
      } else {
        ElMessage.error(response.message || '识别失败，请稍后重试')
      }
    } catch (error) {
      ElMessage.error('系统错误，请稍后重试')
    } finally {
      isSubmitting.value = false
    }
  }
}

onMounted(async () => {
  // Get data from Pinia store instead of route parameters
  const storeData = visitorStore.visitorData
  if (!storeData) {
    router.push('/risk-identification')
    return
  }
  
  visitorData.value = storeData
  // Set the input text from the store
  inputText.value = storeData.inputText || ''
  
  // 获取标签和摘要数据
  const summaryData = await getSummary(visitorData.value.idNumber, visitorData.value.name)
  let isLeft = true
  for (const tag of summaryData.tags) {
    if (isLeft) {
      leftTags.value.push(tag)
    } else {
      rightTags.value.push(tag)
    }
    isLeft = !isLeft
  }
  if (leftTags.value.length == 3) {
    leftTags.value[1].css = 'margin-right: 30px;'
  }
  if (leftTags.value.length == 4) {
    leftTags.value[1].css = 'margin-right: 30px;'
    leftTags.value[2].css = 'margin-right: 30px;'
  }
  if (rightTags.value.length == 3) {
    rightTags.value[1].css = 'margin-left: 30px;'
  }
  if (rightTags.value.length == 4) {
    rightTags.value[1].css = 'margin-left: 30px;'
    rightTags.value[2].css = 'margin-left: 30px;'
  }
  summaryText.value = summaryData.summary

  // 获取推荐案例
  const recommendedCases = await getRecommendedCases(visitorData.value.brief)
  caseList.value = recommendedCases
  caseTabs.value = recommendedCases.map(caseItem => caseItem.summaries[0]?.key || '基本案情')

  // 获取历史来访记录
  const historicalRecords = await getHistoricalVisitRecords(visitorData.value.idNumber, visitorData.value.name)
  visitHistoryRecords.value = historicalRecords
})

const inputText = ref('')
const activeTab = ref('history')

// 案件数据
const caseList = ref<RecommendedCase[]>([])
// 每个案件的tab状态
const caseTabs = ref<string[]>([])

// 获取标签样式类
const getTagClass = (level: string) => {
  switch (level) {
    case 'high':
      return 'red'
    case 'medium':
      return 'orange'
    case 'low':
      return 'blue'
    default:
      return 'red'
  }
}

// 将em标签转换为strong标签
const convertEmToStrong = (content: string) => {
  return content.replace(/<em>/g, '<strong>').replace(/<\/em>/g, '</strong>')
}

// 获取风险等级文本
const getRiskText = (level: string) => {
  switch (level) {
    case 'high':
      return '高风险'
    case 'medium':
      return '中风险'
    case 'low':
      return '低风险'
    default:
      return '低风险'
  }
}

// 类案详情弹出框
const showCaseDialog = ref(false)
const selectedCase = ref<RecommendedCase | null>(null)
const selectedCaseTab = ref('基本案情')
const caseDetailData = ref<GetCaseDetailRes['data'] | null>(null)
const isLoadingCaseDetail = ref(false)

async function openCaseDialog(item: RecommendedCase, idx: number) {
  selectedCase.value = item
  showCaseDialog.value = true
  isLoadingCaseDetail.value = true
  
  try {
    const response = await getCaseDetail(item.wenshuId)
    if (response.code === 0) {
      caseDetailData.value = response.data
      selectedCaseTab.value = response.data.paragraph[0]?.key || '基本案情'
    }
  } catch (error) {
    console.error('Failed to load case detail:', error)
  } finally {
    isLoadingCaseDetail.value = false
  }
}

function closeCaseDialog() {
  showCaseDialog.value = false
  selectedCase.value = null
  selectedCaseTab.value = '基本案情'
  caseDetailData.value = null
}
</script>

<style scoped>
.visitor-portrait-page {
  width: 100%;
  min-height: calc(100vh - 70px);
  background: rgb(248 253 255);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 30px;
  overflow-y: auto;
  height: calc(100vh - 70px);
}

.input-section {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgb(248 253 255);
  padding: 10px 0;
}

.input-box {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 30px;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.08);
  padding: 0 10px;
  width: 900px;
  max-width: 90vw;
  height: 60px;
}

.input-box input {
  border: none;
  outline: none;
  font-size: 18px;
  flex: 1;
  padding: 16px 12px;
  background: transparent;
}

.input-box button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0 8px;
  display: flex;
  align-items: center;
}

.info-area-border {
  border: 2px solid transparent;
  border-radius: 22px;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(to right, #fff, #fff), linear-gradient(to right, rgb(74 177 244),rgb(47 63 245));
}

.info-area {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
  width: 1600px;
  max-width: 98vw;
  background: #fff;
  border-radius: 24px;
  box-shadow: 0 4px 24px rgba(33, 150, 243, 0.10);
  padding: 40px 32px 32px 32px;
  min-height: 600px;
  /* border: 2px solid;
  border-image: linear-gradient(to right, rgb(74 177 244),rgb(47 63 245)) 1; */
}

.visitor-info {
  width: 500px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 64px;
  padding-right: 64px;
}

.avatar-box {
  margin-bottom: 12px;
}

.avatar-img {
  width: 160px;
  height: 160px;
  border-radius: 30%;
  border: 3px solid #b3d0ff;
  background: #f5f8ff;
}

.person-img {
  width: 170px;
  height: 400px;
}

.risk-level {
  margin: 10px 0 18px 0;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  background: none;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
  width: fit-content;
}

.risk-level .risk-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  border-radius: 30%;
  padding: 5px;
  box-sizing: content-box;
}

.risk-level.high .risk-icon {
  background: #e53935;
}

.risk-level.medium .risk-icon {
  background: #f0bc02;
}

.risk-level.low .risk-icon {
  background: #1976d2;
}

.risk-level .risk-text {
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 2px;
}

.risk-level.high .risk-text {
  color: #e53935;
}

.risk-level.medium .risk-text {
  color: #f0bc02;
}

.risk-level.low .risk-text {
  color: #1976d2;
}

.info-table {
  width: 96%;
  font-size: 18px;
  color: #222e50;
  border-collapse: collapse;
  margin-top: 8px;
}

.info-table td {
  padding: 10px 14px;
}

.info-table td:first-child {
  text-align: right;
}

.portrait-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-left: 32px;
  position: relative;
}

.portrait-img {
  margin-bottom: 18px;
}

.person-img-div {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 18px;
  min-height: 320px;
}

.left-tag-list, .right-tag-list {
  flex-direction: column;
}

.left-tag-list {
  margin-right: 60px;
  align-items: flex-end;
}

.right-tag-list {
  margin-left: 60px;
  align-items: flex-start;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 60px;
  margin-bottom: 18px;
}

.tag {
  display: inline-block;
  padding: 12px 36px;
  border-radius: 16px;
  font-size: 20px;
  font-weight: 500;
  background: #e3f2fd;
  color: #1976d2;
  box-shadow: 0 1px 4px rgba(33, 150, 243, 0.08);
}

.tag.orange {
  background: #fff3e0;
  color: #e65100;
  border: 1.5px solid #ffe0b2;
}

.tag.blue {
  background: #e3f2fd;
  color: #1976d2;
  border: 1.5px solid #b3d0ff;
}

.tag.red {
  background: #fff1f0;
  color: #e53935;
  border: 1.5px solid #ffcdd2;
  box-shadow: 0 2px 6px rgba(229, 57, 53, 0.08);
}

.summary-box {
  background: #f7fbff;
  border-radius: 12px;
  padding: 16px 22px;
  color: #e65100;
  font-size: 18px;
  font-weight: 500;
  box-shadow: 0 1px 4px rgba(33, 150, 243, 0.06);
  max-width: 800px;
  text-indent: 2em;
}

.summary-text {
  color: #e53935;
}

.tab-section {
  width: 1600px;
  max-width: 98vw;
  margin: 36px auto 0 auto;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 2px 12px rgba(33, 150, 243, 0.08);
  padding: 0 0 24px 0;
  border: 2px solid rgb(183 235 255);
}

.tab-header-list {
  justify-content:center;
  margin: 32px 36px 0 36px;
  padding-left: 5px;
  padding-right: 5px;
  display: flex;
  background: rgb(190 202 252);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.12);
  border-top: 1px solid rgb(183 235 255);
  border-bottom: 1px solid rgb(183 235 255);
}

.tab-header {
  flex: 1;
  text-align: center;
  padding: 18px 0 12px 0;
  font-size: 18px;
  font-weight: 500;
  color: #4C5674;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 2px solid transparent;
  background: #fff;
  position: relative;
}

.tab-header:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 60%;
  background-color: #d1d5db;
}

.tab-header.active {
  color: #5289e0;
  border-bottom: 2px solid transparent;
}

.tab-header.active::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2.5px;
  background-color: #5289e0;
}

.tab-header.disabled {
  color: #999;
  cursor: not-allowed;
  opacity: 1;
  background: #fff;
}

.tab-header.disabled:hover {
  background: #fff;
}

.tab-content {
  padding: 32px 36px 0 36px;
  font-size: 16px;
  color: #333;
  min-height: 120px;
}

.case-block {
  display: flex;
  flex-direction: column;
  background: #f8fbff;
  border-radius: 14px;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.06);
  margin-bottom: 24px;
  padding: 0;
  min-height: 180px;
}

.case-header {
  background: #E7F8FF;
  color: #222e50;
  font-weight: bold;
  padding: 0 28px 0 28px;
  border-radius: 10px 10px 0 0;
  border-bottom: 1.5px solid #e3f2fd;
  height: 48px;
  line-height: 48px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.case-header:hover {
  background: #e3f2fd;
  color: #1976d2;
}

.case-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.case-source-tag {
  background: #E47470;
  color: #FFFFFF;
  font-size: 12px;
  line-height: 24px;
  font-weight: 500;
  padding: 0 8px;
  border-radius: 6px;
  margin-right: 12px;
  white-space: nowrap;
  flex-shrink: 0;
  box-shadow: 0 1px 3px rgba(94, 126, 255, 0.3);
}

.case-content-area {
  display: flex;
  flex-direction: row;
  width: 100%;
  min-height: 240px; /* 至少显示3个tab的高度 (3 * 80px) */
}

.case-left {
  width: 160px;
  border-right: 1.5px solid #e3f2fd;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  background: #fff;
  border-radius: 14px 0 0 14px;
}

.case-tab-list {
  display: flex;
  flex-direction: column;
  min-height: 240px; /* 至少显示3个tab的高度 */
}

.case-tab {
  padding: 18px 0;
  text-align: center;
  font-size: 16px;
  color: #1976d2;
  cursor: pointer;

  transition: all 0.2s;
  background: #fff;
  height: 60px; /* 固定每个tab的高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.case-tab.active {
  color: #1976d2;
  background: #fff;
  font-weight: bold;
}

.case-tab-content {
  padding: 8px 16px;
  border-radius: 10px;
  transition: all 0.2s;
}

.case-tab.active .case-tab-content {
  background: #5E7EFF;
  color: #FFFFFF;
}

.case-right {
  flex: 1;
  padding: 28px 32px;
  display: flex;
  align-items: flex-start;
  background: #f8fbff;
  border-radius: 0 14px 14px 0;
  overflow-y: auto; /* 内容超出时显示滚动条 */
  max-height: 240px; /* 与左侧tab区域保持一致 */
}

.case-content {
  font-size: 16px;
  color: #333;
  line-height: 1.8;
  white-space: pre-line;
  width: 100%;
}

/* 自定义右侧内容区域的滚动条样式 */
.case-right::-webkit-scrollbar {
  width: 6px;
}

.case-right::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.case-right::-webkit-scrollbar-thumb {
  background: #90caf9;
  border-radius: 3px;
}

.case-right::-webkit-scrollbar-thumb:hover {
  background: #64b5f6;
}

.history-summary {
  color: #e53935;
  font-weight: 500;

  font-size: 17px;
  margin-bottom: 28px;
  padding-left: 12px;
  text-indent: 2em;
}

.timeline-card {
  margin-bottom: 10px;
}

.card-header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.case-name {
  font-size: 16px;
  font-weight: bold;
  color: #222e50;
}

.card-row {
  margin-bottom: 6px;
  font-size: 15px;
  color: #333;
}

:deep(.el-timeline-item__node) {
  background-color: #1976d2;
}

:deep(.el-timeline-item__tail) {
  border-left: 2px solid #1976d2;
}

:deep(.el-timeline-item__timestamp) {
  background: #3286fa;
  color: #fff;
  border-radius: 20px;
  padding: 2px 22px;
  font-weight: bold;
  font-size: 16px;
  display: inline-block;
  min-width: 90px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(50, 134, 250, 0.08);
  margin-bottom: 8px;
}

:deep(.el-card) {
  border: none;
  background: rgb(235 244 249);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.06);
}

:deep(.el-card__body) {
  padding: 18px 24px;
  border-radius: 6px;
  border: 1.5px solid rgb(206 225 232);
}

.timeline-type-tag {
  background: #347BE3 !important;
  color: #fff !important;
  border: none !important;
  border-radius: 0 0 10px 10px !important;
  font-size: 16px !important;
  line-height: 32px;
  height: 32px;
  font-weight: 500;
  padding: 6px 18px 4px 18px;
  position: absolute;
  right: 0;
  top: -18px;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(52, 123, 227, 0.10);
}

@media (max-width: 1200px) {

  .info-area,
  .tab-section {
    width: 98vw;
    padding-left: 8px;
    padding-right: 8px;
  }
}

@media (max-width: 900px) {

  .info-area,
  .tab-section {
    flex-direction: column;
    width: 100vw;
    padding: 0 2vw;
  }

  .visitor-info {
    border-right: none;
    border-bottom: 1.5px solid #e3f2fd;
    padding-right: 0;
    padding-bottom: 18px;
    width: 100%;
  }

  .portrait-area {
    padding-left: 0;
    width: 100%;
  }
}

/* 自定义滚动条样式 */
.visitor-portrait-page::-webkit-scrollbar {
  width: 8px;
}

.visitor-portrait-page::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.visitor-portrait-page::-webkit-scrollbar-thumb {
  background: #90caf9;
  border-radius: 4px;
}

.visitor-portrait-page::-webkit-scrollbar-thumb:hover {
  background: #64b5f6;
}

.ai-assistant-dialog .el-dialog__body {
  padding: 0;
}

.ai-assistant-container {
  display: flex;
  height: 600px;
}

.ai-assistant-sidebar {
  width: 240px;
  background: #f7fbff;
  border-right: 1px solid #e3f2fd;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.ai-assistant-title {
  font-size: 20px;
  font-weight: bold;
  color: #222e50;
  padding: 24px 0 18px 24px;
}

.ai-assistant-conv-list {
  flex: 1;
  overflow-y: auto;
}

.ai-assistant-conv-item {
  padding: 16px 24px;
  cursor: pointer;
  color: #4C5674;
  border-left: 4px solid transparent;
  transition: background 0.2s, border-color 0.2s;
}
.ai-assistant-conv-item.active {
  background: #e3f2fd;
  border-left: 4px solid #5289e0;
  color: #5289e0;
}

.ai-assistant-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.ai-assistant-chat {
  flex: 1;
  padding: 32px 36px 0 36px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.ai-assistant-msg {
  max-width: 80%;
  padding: 14px 18px;
  border-radius: 16px;
  font-size: 16px;
  line-height: 1.7;
  word-break: break-all;
}
.ai-assistant-msg-user {
  align-self: flex-end;
  background: #e3f2fd;
  color: #1976d2;
}
.ai-assistant-msg-bot {
  align-self: flex-start;
  background: #fff1f0;
  color: #e53935;
}

.ai-assistant-input {
  display: flex;
  align-items: center;
  padding: 18px 36px;
  border-top: 1px solid #e3f2fd;
  background: #f7fbff;
}
.ai-assistant-input input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 16px;
  padding: 12px 16px;
  border-radius: 8px;
  background: #fff;
  margin-right: 12px;
  box-shadow: 0 1px 4px rgba(33,150,243,0.06);
}
.ai-assistant-input button {
  background: #5289e0;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 24px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.2s;
}
.ai-assistant-input button:hover {
  background: #347BE3;
}

.ai-assistant-float {
  cursor: pointer;
  z-index: 1000;
  transition: transform 0.2s;
  user-select: none;
}

.ai-assistant-float:active {
  transform: scale(0.95);
}

.ai-assistant-float.dragging {
  transform: scale(1.1) translateY(-50%);
  cursor: grabbing;
  opacity: 0.8;
  transition: none;
}

.empty-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.empty-img {
  width: 200px;
  height: 200px;
  margin-bottom: 20px;
}

.empty-text {
  font-size: 18px;
  color: #909399;
  font-weight: 500;
}

.case-detail-dialog .el-dialog__body {
  padding: 0;
}

:deep(.case-detail-dialog .el-dialog__title) {
  color: #222e50;
  font-weight: bold;
}

:deep(.case-detail-dialog .el-dialog__header) {
  background: #EFF8FD;
  border-radius: 16px 16px 0 0;
  padding: 16px 20px;
  border-bottom: 1.5px solid #e3f2fd;
}

:deep(.case-detail-dialog .el-dialog) {
  border-radius: 16px;
  overflow: hidden;
}

.case-detail-container {
  display: flex;
  min-height: 400px;
}

.case-detail-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  min-height: 400px;
}

.case-detail-left {
  width: 160px;
  border-right: 1px solid #e3f2fd;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  background: #fff;
  border-radius: 14px 0 0 14px;
}

.case-detail-tab-list {
  display: flex;
  flex-direction: column;
}

.case-detail-tab {
  margin-right: 10px;
  padding: 18px 0;
  text-align: center;
  font-size: 16px;
  color: #1976d2;
  cursor: pointer;
  transition: all 0.2s;
  background: #fff;
  min-height: 60px; /* 最小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.case-detail-tab.active {
  color: #1976d2;
  background: #fff;
  font-weight: bold;
}

.case-detail-tab-content {
  padding: 8px 16px;
  border-radius: 10px;
  transition: all 0.2s;
}

.case-detail-tab.active .case-detail-tab-content {
  background: #5E7EFF;
  color: #FFFFFF;
}

.case-detail-right {
  flex: 1;
  padding: 28px 32px;
  display: flex;
  align-items: flex-start;
  background: #f8fbff;
  border-radius: 0 14px 14px 0;
  overflow-y: auto; /* 内容超出时显示滚动条 */
}

.case-detail-text {
  font-size: 16px;
  color: #333;
  line-height: 1.8;
  white-space: pre-line;
  width: 100%;
}

/* 自定义右侧内容区域的滚动条样式 */
.case-detail-right::-webkit-scrollbar {
  width: 6px;
}

.case-detail-right::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.case-detail-right::-webkit-scrollbar-thumb {
  background: #90caf9;
  border-radius: 3px;
}

.case-detail-right::-webkit-scrollbar-thumb:hover {
  background: #64b5f6;
}

.case-detail-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #666;
  font-size: 16px;
  gap: 16px;
}

.case-detail-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #e53935;
  font-size: 16px;
}

.download-report-btn {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
}
</style>