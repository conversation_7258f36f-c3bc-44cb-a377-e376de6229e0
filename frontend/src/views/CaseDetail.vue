<template>
  <div class="case-detail-container">
    <!-- 左侧 PDF 预览，增加蓝色外层和标题 -->
    <div class="main-card left">
      <div class="section-title">文书卷宗</div>
      <div class="pdf-container">
        <VuePdfEmbed 
          :source="pdfSource" 
          :textLayer="true" 
          :allPages="true"
          :scale="1.2"
          :width="680"
          :highlights="highlights"
          :highlightText="highlightText"
          class="pdf-embed"
        />
      </div>
    </div>
    <!-- 右侧案件详情 -->
    <div class="main-card right">
      <div class="section-title">{{ caseInfo.ajmc || '案件详情' }}</div>
      <div class="case-info">
        <!-- 主卡片 -->
        <!-- 风险识别情况子卡片 -->
        <div class="sub-card">
          <h3 class="sub-card-title risk-title">风险识别情况</h3>
          <div class="risk-items">
            <span
              v-for="(item, idx) in tags"
              :key="idx"
              class="risk-tag clickable"
              @click="showTagText(item.text)"
            >
              {{idx+1}}、{{ item.tag }}
            </span>
          </div>
        </div>
        <!-- 案件基本信息子卡片 -->
        <div class="sub-card">
          <h3 class="sub-card-title">案件基本信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">承办人：</span>
              <span class="value">{{ caseInfo.cbjcg }}</span>
            </div>
            <div class="info-item">
              <span class="label">承办部门：</span>
              <span class="value">{{ caseInfo.cbbmMc }}</span>
            </div>
            <div class="info-item">
              <span class="label">承办单位：</span>
              <span class="value">{{ caseInfo.cbdwMc }}</span>
            </div>
            <div class="info-item">
              <span class="label">受理时间：</span>
              <span class="value">{{ formatDate(caseInfo.slrq) }}</span>
            </div>
          </div>
          <div class="case-summary">
            <span class="label">案情摘要：</span>
            <div class="summary-content">
              {{ caseInfo.aqzy }}
            </div>
          </div>
        </div>
        <!-- 犯罪嫌疑人子卡片 -->
        <div class="sub-card suspects-card">
          <div class="suspects-tabs">
            <div 
              v-for="(suspect, index) in suspects" 
              :key="suspect.zrrbm"
              :class="['suspect-tab', { active: activeSuspectIndex === index }]"
              @click="activeSuspectIndex = index"
            >
              犯罪嫌疑人：{{ suspect.xm }}
            </div>
          </div>
          <div class="suspect-info">
            <span class="label">案由：</span>
            <span class="value">{{ suspects[activeSuspectIndex]?.aymc }}</span>
          </div>
        </div>
      </div>
    </div>

    <AIAssistantDialog v-model="showAIDialog" />
        <!-- 悬浮AI助手图标 -->
    <AIAssistantFloat @click="handleAIAssistantClick" />
          <!-- AI助手对话框 -->

    <!-- 底部固定操作按钮区域 -->
    <div class="fixed-action-buttons">
      <el-button class="push-btn" @click="onPush">推送</el-button>
      <el-button type="danger" @click="onDelete">删除</el-button>
      <el-button class="back-btn" @click="onBack">返回</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import VuePdfEmbed from '@/components/VuePdfEmbed'
import AIAssistantDialog from '@/components/CaseRisk/AIAssistantDialog.vue'
import AIAssistantFloat from '@/components/CaseRisk/AIAssistantFloat.vue'
import SideBar from '@/components/SideBar.vue'
import { getCaseDetail, getPdfStream, CaseDetailData, Suspect, Tag } from '@/api/caseDetail';

const showAIDialog = ref(false)

// 移除axios相关配置
// console.log('当前环境变量:', import.meta.env);
// console.log('API基础URL:', import.meta.env.VITE_API_BASE_URL);
// axios.defaults.baseURL = import.meta.env.VITE_API_BASE_URL;

const route = useRoute();
const router = useRouter();
const highlights = [
{
      "pageNumber": 1,
      "x": 85.92,
      "y": 611.39026,
      "width": 252.36284,
      "height": 15,
      "text": "经甲、乙双方平等、自愿协商同意，对本合同做以下变更",
      "pageWidth": 595.3,
      "pageHeight": 841.9
    }
]
const highlightText = ref('')
const caseInfo = ref<CaseDetailData>({
        bmsah: '',
        ajmc: '',
        cbdwMc: '',
        slrq: null,
        cbjcg: '',
        cbbmMc: '',
        tag: '',
        fxsbqk: '',
        aqzy: '',
        xyrList: [],
        tags: []
});

const activeSuspectIndex = ref(0);

const suspects = ref<Suspect[]>([]);

const caseDetail = ref<CaseDetailData | null>(null);

const tags = ref<Tag[]>([]);

const tagsDisplayStr = ref('')

const pdfSource = ref('')

console.log('CaseDetail组件初始化');

const handleAIAssistantClick = () => {
  showAIDialog.value = true
}

// 获取PDF文件流
async function loadPdfStream(bmsah: string) {
  try {
    const blob = await getPdfStream(bmsah);
    
    if (!blob) {
      ElMessage.error('获取PDF文件失败');
      return;
    }
    
    // 检查文件类型
    if (blob.type !== 'application/pdf') {
      ElMessage.warning('文件格式不正确，请确保是PDF文件');
      return;
    }
    
    // 创建 Blob URL
    const blobUrl = URL.createObjectURL(blob);
    
    // 更新 PDF 源
    pdfSource.value = blobUrl;
  } catch (error) {
    console.error('获取PDF文件失败:', error);
    ElMessage.error('获取PDF文件失败');
  }
}

async function loadCaseDetail(bmsah: string) {
  try {
    const response = await getCaseDetail(bmsah);
    if (response && response.code === 0) {
      console.log('响应成功，更新数据');
      caseDetail.value = response.data;
      caseInfo.value = response.data;
      suspects.value = response.data.xyrList;
      // tag分割、拼接案件名称并换行
      tags.value = response.data.tags;

      console.log(tagsDisplayStr.value);
      console.log(suspects.value);

      // 加载PDF文件
      await loadPdfStream(bmsah);
    } else {
      console.log('响应失败，显示错误消息');
      ElMessage.error('获取案件详情失败');
    }
  } catch (error) {
    console.error('请求出错:', error);
    ElMessage.error('获取案件详情失败');
  }
}

onMounted(() => {
  console.log('CaseDetail组件已挂载');
  console.log('当前路由参数:', route.params);
  const bmsah = route.params.bmsah as string;
  console.log('获取到的bmsah参数:', bmsah);
  if (bmsah) {
    console.log('开始调用loadCaseDetail');
    loadCaseDetail(bmsah);
  } else {
    console.error('未获取到bmsah参数');
    ElMessage.error('未获取到案件编号');
  }
});

function onPush() {
  ElMessage.info('暂未开放');
}

function onDelete() {
  ElMessage.info('暂未开放');
}

function onBack() {
  router.push('/source-identification')
}

function showTagText(text: string) {
  if (!text) return;
  highlightText.value = text;
}

function formatDate(val: string | null) {
  if (!val) return '';
  // 兼容 ISO 字符串和带空格的格式
  const dateStr = val.split(' ')[0].split('T')[0];
  return dateStr;
}
</script>

<style scoped  lang="scss">
.case-detail-container {
  display: flex;
  gap: 20px;
  padding: 20px;
  height: calc(100vh - 102px);
  background: #f5f7fa;
}

.main-card {
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #eaf3ff 0%, #c6e0ff 100%);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(14, 47, 106, 0.10);
  padding: 0 0 20px 0;
  min-width: 0;
  height: 100%;
  overflow: hidden;
}

.main-card.left {
  max-width: 750px;
  min-width: 400px;
  width: 100%;
  flex: none;
}

.main-card.right {
  flex: 1;
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  color: black;
  background: #eaf3ff;
  border-top-left-radius: 12px;
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  border-top-right-radius: 12px;
  padding: 0px 24px 0px 24px;
  letter-spacing: 1px;
}

.pdf-container {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(14, 47, 106, 0.10);
  overflow-y: auto;
  margin: 5px 20px 0 20px;
  scroll-behavior: smooth;
  
  .pdf-embed {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      transition: width 0.3s ease; // 添加过渡效果
      
      :deep(.canvasWrapper) {
        canvas {
          max-width: none !important;
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;
        }
      }
    }
    
}

.case-info {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(14, 47, 106, 0.10);
  overflow-y: auto;
  padding: 20px;
  margin: 5px 20px 0 20px;
  scroll-behavior: smooth;
}

.vue-pdf-embed {
  & > div {
    margin-bottom: 4px;
  }

  canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

.main-card-title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px 24px 32px;
  border-radius: 16px 16px 0 0;
  background: #eaf6ff;
}

.case-title-section {
  display: flex;
  align-items: center;
}

.case-circle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(80deg, #39D0F8 0%, #268EF5 80%);
  margin-right: 12px;
}

.case-name {
  font-size: 26px;
  font-weight: bold;
  color: #222;
}

.case-tags {
  display: flex;
  align-items: center;
}

.tag-mix {
  display: flex;
  align-items: center;
  border-radius: 14px;
  overflow: hidden;
  font-size: 20px;
  font-weight: 600;
  height: 40px;
  background: #fff7e6;
  padding: 16px;
}

.tag-left, .tag-right {
  background: transparent;
  color: #ff9900;
  padding: 0 10px;
  height: 28px;
  display: flex;
  align-items: center;
}

.tag-divider {
  width: 1px;
  height: 16px;
  background: #ffc04d;
  display: inline-block;
  margin: 0 2px;
}

.sub-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px 0 rgba(36, 100, 170, 0.10), 0 1.5px 6px 0 rgba(36, 100, 170, 0.08);
  padding: 20px 32px 20px 32px;
  margin: 0 32px 0 32px;
}

.sub-card-title {
  font-size: 20px;
  font-weight: bold;
  color: #222;
  margin: 0 0 20px 0;
}

.risk-title {
  color: #FB9602;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px 32px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 20px;
}

.info-item .label {
  color: #333;
  margin-right: 8px;
  min-width: 100px;
  font-size: 20px;
}

.info-item .value {
  color: #666;
  font-size: 20px;
}

.case-summary {
  border-top: 1px solid #e4e7ed;
  padding-top: 20px;
}

.case-summary .label {
  font-weight: bold;
  color: #333;
  font-size: 20px;
  display: inline-block;
  margin-bottom: 12px;
}

.summary-content {
  padding: 16px;
  color: #666;
  font-size: 20px;
  line-height: 1.6;
  min-height: 80px;
}

.suspects-tabs {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
  gap: 24px;
}

.suspects-card {
  padding: 10px 32px 20px 32px;
}

.suspect-tab {
  padding: 12px;
  font-size: 20px;
  color: black;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
  white-space: nowrap;
  cursor: pointer;
}

.suspect-tab.active {
  color: #409eff;
  border-bottom-color: #409eff;
  font-weight: 600;
}

.suspect-tab:hover:not(.active) {
  color: #409eff;
}

.more-suspects {
  padding: 12px;
  color: #999;
  font-size: 20px;
  cursor: pointer;
}

.suspect-info {
  display: flex;
  align-items: center;
  font-size: 20px;
}

.suspect-info .label {
  font-weight: bold;
  color: #333;
  margin-right: 8px;
  font-size: 20px;
}

.suspect-info .value {
  color: #666;
  font-size: 20px;
}

.risk-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.risk-tag {
  display: inline-block;
  padding: 8px 16px;
  background: #eaf3ff;
  color: #2563eb;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  margin-bottom: 4px;
  user-select: none;
  &:hover {
    background: #2563eb;
    color: #fff;
  }
  &:active {
    background: #1749b1;
    color: #fff;
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin: 10px;
}

.el-button {
  border-radius: 18px !important;
  font-size: 15px;
  padding: 0 24px;
  height: 36px;
}

.el-button--danger {
  background: #fff !important;
  border: 1.5px solid #f56c6c !important;
  color: #f56c6c !important;
}

.el-button--danger:hover, .el-button--danger:focus {
  background: #fff0f0 !important;
  border-color: #b71c1c !important;
  color: #b71c1c !important;
}

.el-button.back-btn {
  background: #fff !important;
  border: 1.5px solid #409eff !important;
  color: #409eff !important;
}

.el-button.back-btn:hover, .el-button.back-btn:focus {
  background: #f0f7ff !important;
  border-color: #1765ad !important;
  color: #1765ad !important;
}

.el-button.push-btn {
  background: linear-gradient(90deg, #39D0F8 0%, #268EF5 100%) !important;
  color: #fff !important;
  border: none !important;
}

.el-button.push-btn:hover, .el-button.push-btn:focus {
  background: linear-gradient(90deg, #268EF5 0%, #39D0F8 100%) !important;
  color: #fff !important;
}

.fixed-action-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f5f7fa;
  gap: 32px;
}
</style> 