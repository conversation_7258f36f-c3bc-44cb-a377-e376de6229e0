import request from './request'

/**
 * 嫌疑人信息接口
 */
export interface Suspect {
  zrrbm: string; // 责任人编码
  xm: string;    // 姓名
  aymc: string;  // 案由名称
  [key: string]: any; // 其他可能的属性
}

/**
 * 标签信息接口
 */
export interface Tag {
  tag: string;   // 标签内容
  text: string;  // 标签相关文本
  [key: string]: any; // 其他可能的属性
}

/**
 * 案件详情数据接口
 */
export interface CaseDetailData {
  bmsah: string;  // 编码
  ajmc: string;   // 案件名称
  cbdwMc: string; // 承办单位名称
  slrq: string | null; // 受理日期
  cbjcg: string;  // 承办检察官
  cbbmMc: string; // 承办部门名称
  tag: string;    // 标签
  fxsbqk: string; // 风险识别情况
  aqzy: string;   // 案情摘要
  xyrList: Suspect[]; // 嫌疑人列表
  tags: Tag[];    // 标签列表
  [key: string]: any; // 其他可能的属性
}

/**
 * 案件详情响应接口
 */
export interface CaseDetailResponse {
  code: number;
  message: string;
  data: CaseDetailData;
}

/**
 * 获取案件详情
 * @param bmsah 案件编号
 * @returns 案件详情数据
 */
export const getCaseDetail = async (bmsah: string): Promise<CaseDetailResponse> => {
  try {
    const response = await request.get(`/xffxytsbyfk/getTyywAjxx/${encodeURIComponent(bmsah)}`);
    return response.data;
  } catch (error) {
    console.error('获取案件详情失败:', error);
    return {
      code: -1,
      message: '获取案件详情失败',
      data: {} as CaseDetailData
    };
  }
}

/**
 * 获取PDF文件流
 * @param bmsah 案件编号
 * @returns Blob对象
 */
export const getPdfStream = async (bmsah: string): Promise<Blob | null> => {
  try {
    const response = await request.get(`/xffxytsbyfk/download/stream/${encodeURIComponent(bmsah)}`, {
      responseType: 'blob'
    });
    
    if (response.data) {
      return new Blob([response.data], { type: 'application/pdf' });
    }
    return null;
  } catch (error) {
    console.error('获取PDF文件失败:', error);
    return null;
  }
} 