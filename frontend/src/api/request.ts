import axios from 'axios'

// 创建统一的axios实例
const request = axios.create({
  baseURL: `${import.meta.env.VITE_API_BASE_URL || ''}/api`,
  // 添加withCredentials以支持跨域请求时发送cookie
  withCredentials: true,
  // 设置默认超时时间（10秒）
  // 可以在各API调用中通过传入 { timeout: 自定义毫秒数 } 来覆盖此设置
  timeout: 10000
})

// 用于防止多次重定向的标志
let isRedirecting = false

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      // 设置请求头Authorization
      config.headers['Authorization'] = `Bearer ${token}`
    }
    
    // 只有非blob类型的请求才设置这些头部
    if (config.responseType !== 'blob') {
      // 确保请求头包含必要的CORS相关字段
      config.headers['Content-Type'] = 'application/json'
      config.headers['Accept'] = 'application/json'
    } else {
      // 对于blob类型请求，设置适当的Accept头部
      config.headers['Accept'] = '*/*'
    }
    
    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    return response
  },
  error => {
    console.error('API错误:', error)
    
    // 检查是否是CORS错误
    if (error.message && error.message.includes('Network Error')) {
      console.error('可能是CORS错误:', error)
      // 不要在这里重定向，让调用方处理错误
    } else if (error.response) {
      // 处理不同的错误状态码
      switch (error.response.status) {
        case 401: // 未授权
          // 清除token和用户信息
          localStorage.removeItem('token')
          localStorage.removeItem('userName')
          localStorage.removeItem('userLoginId')
          
          // 防止重复重定向
          if (!isRedirecting) {
            isRedirecting = true
            
            // 使用window.location重定向到登录页面
            const currentPath = window.location.pathname
            if (currentPath !== '/login') {
              console.log('Token失效，重定向到登录页面')
              // 保存当前路径，以便登录后可以返回
              window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`
            } else {
              isRedirecting = false
            }
          }
          break
          
        case 403: // 禁止访问
          console.error('无权限访问此资源:', error)
          break
          
        case 404: // 资源不存在
          console.error('请求的资源不存在:', error)
          break
          
        case 500: // 服务器错误
          console.error('服务器内部错误:', error)
          break
          
        default:
          console.error(`未处理的错误状态码 ${error.response.status}:`, error)
      }
    } else if (error.request) {
      // 请求发送了但没有收到响应
      console.error('未收到响应:', error.request)
    }
    
    return Promise.reject(error)
  }
)

export default request 