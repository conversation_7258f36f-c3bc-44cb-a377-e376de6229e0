import request from './request'

export interface CaseListResponse {
    data: Array<{
        bmsah: number;
        ajmc: string;
        type: string;
        slrq: Date;
        cbjcg: string;
        cbbmMc: string;
        tag: string;
    }>;
    total: number;
    size: number;
    current: number;
}

export interface CaseListSearchParams {
    page: number;
    pageSize: number;
    searchDTO: {
        ajmc?: string;
        xyrxm?: string;
        cbbmMc?: string;
        cbjcg?: string;
        aymc?: string;
        slrqStart?: string;
        slrqEnd?: string;
    };
}

export const getCaseList = async (params: CaseListSearchParams): Promise<CaseListResponse> => {
    try {
        const response = await request.post('/xffxytsbyfk/tyywAjxxList', params.searchDTO, {
            params: {
                page: params.page,
                pageSize: params.pageSize
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching case list:', error);
        return {
            data: [],
            total: 0,
            size: params.pageSize,
            current: params.page
        };
    }
} 