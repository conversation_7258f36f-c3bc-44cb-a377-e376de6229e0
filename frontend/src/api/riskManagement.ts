import request from './request'
import mockData from '@/mock/data.json'

// 定义旧的VisitTypes接口，与mock数据兼容
interface OldVisitTypes {
    email: { count: string; label: string };
    wechat: { count: string; label: string };
    online: { count: string; label: string };
    phone: { count: string; label: string };
}

// 定义mockData的接口
interface MockData {
    visitTypes: OldVisitTypes;
    visitTopTypes: VisitTopTypes;
    crimeTypes: CrimeTypes;
    mapData: any;
    overview: OverviewData;
    visitFields: any[];
    caseAnalysis: { data: any[] };
    personData?: any[]; // 可能不存在
}

// 类型断言，将导入的mockData转换为我们定义的接口
const typedMockData = mockData as MockData;

// 默认的人员数据，当mockData中不存在personData时使用
const defaultPersonData = [
    { name: '涉非公企业', value: 564, x: 250, y: 250 },
    { name: '涉未成年人', value: 295, x: 420, y: 260 },
    { name: '涉港澳台', value: 207, x: 500, y: 320 },
    { name: '涉检察人员', value: 76, x: 400, y: 350 },
    { name: '涉农', value: 41, x: 350, y: 390 },
    { name: '涉律师', value: 30, x: 450, y: 410 },
    { name: '涉军', value: 18, x: 550, y: 400 },
    { name: '涉公有制企业', value: 2, x: 600, y: 330 },
    { name: '涉医', value: 37, x: 650, y: 260 },
    { name: '涉教', value: 18, x: 650, y: 380 },
    { name: '农村务工人员', value: 29, x: 650, y: 380 },
    { name: '货车司机', value: 7, x: 650, y: 380 },
    { name: '在校大学生', value: 19, x: 650, y: 380 },
    { name: '村（社区）两委人员', value: 12, x: 650, y: 380 },
    { name: '快递外卖人员', value: 4, x: 650, y: 380 },
    { name: '其他', value: 3539, x: 650, y: 380 }
];

/**
 * 配置变量：是否使用后端接口
 * 设置为true时使用后端接口获取数据
 * 设置为false时使用前端模拟数据
 */
export const USE_BACKEND = false

/**
 * 日期范围请求接口
 */
export interface DateRangeRequest {
    startDate: string // YYYY-MM-DD格式
    endDate: string // YYYY-MM-DD格式
}

/**
 * 通用名称-值数据项
 */
export interface NameValueItem {
    name: string
    value: number
    key?: string // 可选，用于获取图标等
}

/**
 * 涉检情况分析数据项
 */
export interface CaseAnalysisItem {
    name: string
    value: number
    percentage?: number // 占比
    zb?: number // 后端返回的占比字段，类型改为number
}

/**
 * 信访总体概况响应
 */
export interface VisitOverviewResponse {
    qsxfl: string // 全市信访量
    qsxfltb: number // 全市信访量同比
    gzsyxfl: string // 广州市院信访量
    gzsyxfltb: number // 广州市院信访量同比
}

/**
 * 前端访问渠道项
 */
export interface VisitChannelItem {
    key: string     // 唯一标识符
    count: string   // 数量
    label: string   // 显示标签
    icon?: string   // 可选的图标标识，用于前端匹配图标
}

/**
 * 前端信访渠道数据结构 - 数组结构支持动态数量
 */
export interface VisitChannels {
    items: VisitChannelItem[]
}

/**
 * 前端风险类型2数据结构
 */
export interface VisitTopTypes {
    top1: VisitChannelItem
    top2: VisitChannelItem
    top3: VisitChannelItem
    top4: VisitChannelItem
    top5: VisitChannelItem
}

/**
 * 前端信访总体概况数据结构
 */
export interface OverviewData {
    totalVisits: string
    yearOnYearGrowth: {
        value: string
        isIncrease: boolean
    }
    guangzhouVisits: string
    guangzhouGrowth: {
        value: string
        isIncrease: boolean
    }
}

/**
 * 前端罪名类型数据结构
 */
export interface CrimeTypes {
    items: NameValueItem[]
}

/**
 * 获取所有风险管理数据结果类型
 */
export interface RiskManagementData {
    visitChannels: VisitChannels;
    visitTopTypes: VisitTopTypes;
    crimeTypes: CrimeTypes;
    mapData: any;
    overview: OverviewData;
    visitFields: any[];
    personData: any[];
    caseAnalysis: { data: any[] };
}

/**
 * 信访渠道分析
 * @param dateRange 日期范围
 * @returns 信访渠道数据
 */
export const getVisitChannelAnalysis = async (dateRange: DateRangeRequest): Promise<VisitChannels> => {
    // 从前端模拟数据获取
    if (!USE_BACKEND) {
        // 转换老的模拟数据格式到新的数组格式
        const oldTypes = typedMockData.visitTypes;
        const items = [
            { key: 'email', count: oldTypes.email.count, label: oldTypes.email.label, icon: 'duanxin' },
            { key: 'wechat', count: oldTypes.wechat.count, label: oldTypes.wechat.label, icon: 'laifang' },
            { key: 'online', count: oldTypes.online.count, label: oldTypes.online.label, icon: 'wangluo' },
            { key: 'phone', count: oldTypes.phone.count, label: oldTypes.phone.label, icon: 'dianhua' }
        ];
        
        // 按count从大到小排序
        items.sort((a: VisitChannelItem, b: VisitChannelItem) => parseInt(b.count) - parseInt(a.count));
        
        return {
            items: items
        };
    }

    // 从后端获取数据
    try {
        const response = await request.post('/riskManagement/visitChannelAnalysis', dateRange)
        
        // 转换为前端需要的格式
        const items = response.data.map((item: NameValueItem) => {
            // 根据渠道名称设置合适的key和icon
            let key = item.key || '';
            let icon = item.key || '';
            
            // 如果没有key，根据name创建唯一标识和图标标识
            if (!key) {
                // 根据name确定合适的key和icon
                const name = item.name.toLowerCase();
                if (name.includes('来信') || name.includes('信件')) {
                    key = 'email';
                    icon = 'duanxin';
                } else if (name.includes('来访') || name.includes('访问')) {
                    key = 'wechat';
                    icon = 'laifang';
                } else if (name.includes('网络') || name.includes('网上')) {
                    key = 'online';
                    icon = 'wangluo';
                } else if (name.includes('电话') || name.includes('12309')) {
                    key = 'phone';
                    icon = 'dianhua';
                } else {
                    // 默认情况，使用name作为key的一部分
                    key = `type-${name}`;
                    icon = 'duanxin'; // 默认图标
                }
            }
            
            return {
                key: key,
                count: item.value.toString(),
                label: item.name,
                icon: icon
            };
        });
        
        // 按count从大到小排序
        items.sort((a: VisitChannelItem, b: VisitChannelItem) => parseInt(b.count) - parseInt(a.count));
        
        return {
            items: items
        };
    } catch (error) {
        console.error('获取信访渠道分析数据失败:', error)
        // 返回空数据结构
        return {
            items: []
        }
    }
}

/**
 * 信访风险类型分析2
 * @param dateRange 日期范围
 * @returns 信访风险类型2数据
 */
export const getVisitTopTypeAnalysis = async (dateRange: DateRangeRequest): Promise<VisitTopTypes> => {
    // 从前端模拟数据获取
    if (!USE_BACKEND) {
        // 确保模拟数据中的项目包含key字段
        const oldData = typedMockData.visitTopTypes;
        return {
            top1: { key: 'top1', count: oldData.top1.count, label: oldData.top1.label },
            top2: { key: 'top2', count: oldData.top2.count, label: oldData.top2.label },
            top3: { key: 'top3', count: oldData.top3.count, label: oldData.top3.label },
            top4: { key: 'top4', count: oldData.top4.count, label: oldData.top4.label },
            top5: { key: 'top5', count: oldData.top5.count, label: oldData.top5.label }
        };
    }

    // 从后端获取数据
    try {
        const response = await request.post('/riskManagement/visitTopTypeAnalysis', dateRange)
        
        // 转换为前端需要的格式
        const result: VisitTopTypes = {
            top1: { key: 'top1', count: "0", label: "重复访" },
            top2: { key: 'top2', count: "0", label: "越级访" },
            top3: { key: 'top3', count: "0", label: "缠闹访" },
            top4: { key: 'top4', count: "0", label: "极端访" },
            top5: { key: 'top5', count: "0", label: "集体访" }
        }
        
        // 遍历后端返回的数据，填充前5个数据
        response.data.slice(0, 5).forEach((item: NameValueItem, index: number) => {
            const key = `top${index + 1}` as keyof VisitTopTypes
            result[key] = {
                key: key,
                count: item.value.toString(),
                label: item.name
            }
        })
        
        return result
    } catch (error) {
        console.error('获取信访风险类型分析2数据失败:', error)
        // 返回空数据结构
        return {
            top1: { key: 'top1', count: "0", label: "重复访" },
            top2: { key: 'top2', count: "0", label: "越级访" },
            top3: { key: 'top3', count: "0", label: "缠闹访" },
            top4: { key: 'top4', count: "0", label: "极端访" },
            top5: { key: 'top5', count: "0", label: "集体访" }
        }
    }
}

/**
 * 信访事项涉及罪名
 * @param dateRange 日期范围
 * @returns 信访事项涉及罪名数据
 */
export const getCrimeTypeAnalysis = async (dateRange: DateRangeRequest): Promise<CrimeTypes> => {
    // 从前端模拟数据获取
    if (!USE_BACKEND) {
        return typedMockData.crimeTypes
    }

    // 从后端获取数据
    try {
        const response = await request.post('/riskManagement/crimeTypeAnalysis', dateRange)
        
        // 转换为前端需要的格式
        return {
            items: response.data
        }
    } catch (error) {
        console.error('获取信访事项涉及罪名数据失败:', error)
        // 返回空数据结构
        return {
            items: []
        }
    }
}

/**
 * 地图数据
 * @param dateRange 日期范围
 * @returns 地图数据
 */
export const getMapData = async (dateRange: DateRangeRequest): Promise<any> => {
    // 从前端模拟数据获取
    if (!USE_BACKEND) {
        return typedMockData.mapData
    }

    // 从后端获取数据
    try {
        const response = await request.post('/riskManagement/mapData', dateRange)
        
        // 转换为前端需要的格式
        return {
            areas: response.data.map((item: NameValueItem) => ({
                name: item.name,
                value: item.value.toString()
            }))
        }
    } catch (error) {
        console.error('获取地图数据失败:', error)
        // 返回空数据结构
        return {
            areas: []
        }
    }
}

/**
 * 全市信访量和广州市院信访量
 * @param dateRange 日期范围
 * @returns 信访量概况数据
 */
export const getVisitOverview = async (dateRange: DateRangeRequest): Promise<OverviewData> => {
    // 从前端模拟数据获取
    if (!USE_BACKEND) {
        return typedMockData.overview
    }

    // 从后端获取数据
    try {
        const response = await request.post('/riskManagement/visitOverview', dateRange)
        
        // 转换为前端需要的格式
        const data = response.data
        return {
            totalVisits: data.qsxfl,
            yearOnYearGrowth: {
                value: Math.abs(data.qsxfltb).toFixed(1) + '%',
                isIncrease: data.qsxfltb > 0
            },
            guangzhouVisits: data.gzsyxfl,
            guangzhouGrowth: {
                value: Math.abs(data.gzsyxfltb).toFixed(1) + '%',
                isIncrease: data.gzsyxfltb > 0
            }
        }
    } catch (error) {
        console.error('获取信访量概况数据失败:', error)
        // 返回空数据结构
        return {
            totalVisits: "0",
            yearOnYearGrowth: {
                value: "0%",
                isIncrease: false
            },
            guangzhouVisits: "0",
            guangzhouGrowth: {
                value: "0%",
                isIncrease: false
            }
        }
    }
}

/**
 * 信访领域分析
 * @param dateRange 日期范围
 * @returns 信访领域分析数据
 */
export const getVisitFieldAnalysis = async (dateRange: DateRangeRequest): Promise<any[]> => {
    // 从前端模拟数据获取
    if (!USE_BACKEND) {
        return typedMockData.visitFields
    }

    // 从后端获取数据
    try {
        const response = await request.post('/riskManagement/visitFieldAnalysis', dateRange)
        
        // 转换为前端需要的格式，保留原模拟数据中的x, y坐标
        return response.data.map((item: NameValueItem, index: number) => {
            // 尝试找到模拟数据中对应名称的项
            const mockItem = typedMockData.visitFields.find(f => f.name === item.name)
            
            return {
                name: item.name,
                value: item.value,
                // 如果在模拟数据中找到对应项，使用其x,y值，否则使用默认值或随机值
                x: mockItem ? mockItem.x : 250 + (index % 5) * 100,
                y: mockItem ? mockItem.y : 250 + Math.floor(index / 5) * 50
            }
        })
    } catch (error) {
        console.error('获取信访领域分析数据失败:', error)
        // 返回空数据结构
        return []
    }
}

/**
 * 信访人身分析
 * @param dateRange 日期范围
 * @returns 信访人身分析数据
 */
export const getPersonAnalysis = async (dateRange: DateRangeRequest): Promise<any[]> => {
    // 从前端模拟数据获取
    if (!USE_BACKEND) {
        return typedMockData.personData || defaultPersonData;
    }

    // 从后端获取数据
    try {
        const response = await request.post('/riskManagement/personAnalysis', dateRange)
        
        // 转换为前端需要的格式，保留原模拟数据中的x, y坐标
        return response.data.map((item: NameValueItem, index: number) => {
            // 尝试找到模拟数据中对应名称的项
            const mockPersonData = typedMockData.personData || [];
            const mockItem = mockPersonData.find((p: any) => p.name === item.name) || 
                             typedMockData.visitFields.find(f => f.name === item.name);
            
            return {
                name: item.name,
                value: item.value,
                // 如果在模拟数据中找到对应项，使用其x,y值，否则使用默认值或随机值
                x: mockItem ? mockItem.x : 250 + (index % 5) * 100,
                y: mockItem ? mockItem.y : 250 + Math.floor(index / 5) * 50
            }
        })
    } catch (error) {
        console.error('获取信访人身分析数据失败:', error)
        // 返回空数据结构
        return []
    }
}

/**
 * 涉检情况分析(成案)
 * @param dateRange 日期范围
 * @returns 涉检情况分析数据
 */
export const getCaseAnalysis = async (dateRange: DateRangeRequest): Promise<any> => {
    // 从前端模拟数据获取
    if (!USE_BACKEND) {
        // 将模拟数据中的percentage字符串转换为数值
        return {
            data: typedMockData.caseAnalysis.data.map((item: any) => ({
                name: item.name,
                value: item.value,
                // 去掉百分号并转换为数值
                percentage: parseFloat(item.percentage.replace('%', '')) / 100
            }))
        }
    }

    // 从后端获取数据
    try {
        const response = await request.post('/riskManagement/caseAnalysis', dateRange)
        
        // 转换为前端需要的格式
        return {
            data: response.data.map((item: CaseAnalysisItem) => ({
                name: item.name,
                value: item.value,
                percentage: item.zb // 直接使用后端返回的zb数值作为percentage
            }))
        }
    } catch (error) {
        console.error('获取涉检情况分析数据失败:', error)
        // 返回空数据结构
        return {
            data: []
        }
    }
}

export const getCaseAnalysisTest = async (dateRange: DateRangeRequest): Promise<any> => {

    // 从后端获取数据
    try {
        const response = await request.post('/riskManagement/caseAnalysis', dateRange)
        
        // 转换为前端需要的格式
        return {
            data: response.data.map((item: CaseAnalysisItem) => ({
                name: item.name,
                value: item.value,
                percentage: item.zb // 直接使用后端返回的zb数值作为percentage
            }))
        }
    } catch (error) {

        return {
            data: []
        }
    }
}

/**
 * 获取所有风险管理数据
 * @param startDate 开始日期 YYYY-MM-DD格式
 * @param endDate 结束日期 YYYY-MM-DD格式
 * @returns 包含所有风险管理数据的对象
 */
export const getAllRiskManagementData = async (startDate: string, endDate: string): Promise<RiskManagementData> => {
    const dateRange: DateRangeRequest = { startDate, endDate }
    
    // 如果使用前端模拟数据
    if (!USE_BACKEND) {
        // 处理模拟数据中的caseAnalysis，确保格式一致
        const processedCaseAnalysis = {
            data: typedMockData.caseAnalysis.data.map((item: any) => ({
                name: item.name,
                value: item.value,
                // 去掉百分号并转换为数值
                percentage: parseFloat(item.percentage.replace('%', '')) / 100
            }))
        }
        
        // 转换模拟数据中的visitTypes为新格式
        const convertedVisitTypes = {
            items: [
                { key: 'email', count: typedMockData.visitTypes.email.count, label: typedMockData.visitTypes.email.label, icon: 'duanxin' },
                { key: 'wechat', count: typedMockData.visitTypes.wechat.count, label: typedMockData.visitTypes.wechat.label, icon: 'laifang' },
                { key: 'online', count: typedMockData.visitTypes.online.count, label: typedMockData.visitTypes.online.label, icon: 'wangluo' },
                { key: 'phone', count: typedMockData.visitTypes.phone.count, label: typedMockData.visitTypes.phone.label, icon: 'dianhua' }
            ]
        };
        
        // 对渠道数据按count从大到小排序
        convertedVisitTypes.items.sort((a: VisitChannelItem, b: VisitChannelItem) => parseInt(b.count) - parseInt(a.count));
        
        // 确保visitTopTypes中的每个元素都有key字段
        const oldTopTypes = typedMockData.visitTopTypes;
        const convertedVisitTopTypes = {
            top1: { key: 'top1', count: oldTopTypes.top1.count, label: oldTopTypes.top1.label },
            top2: { key: 'top2', count: oldTopTypes.top2.count, label: oldTopTypes.top2.label },
            top3: { key: 'top3', count: oldTopTypes.top3.count, label: oldTopTypes.top3.label },
            top4: { key: 'top4', count: oldTopTypes.top4.count, label: oldTopTypes.top4.label },
            top5: { key: 'top5', count: oldTopTypes.top5.count, label: oldTopTypes.top5.label }
        };
        getCaseAnalysisTest(dateRange)
        // 返回模拟数据
        return {
            visitChannels: convertedVisitTypes,
            visitTopTypes: convertedVisitTopTypes,
            crimeTypes: typedMockData.crimeTypes,
            mapData: typedMockData.mapData,
            overview: typedMockData.overview,
            visitFields: typedMockData.visitFields,
            personData: typedMockData.personData || defaultPersonData,
            caseAnalysis: processedCaseAnalysis
        }
    }
    
    try {
        // 并行请求所有数据
        const [
            visitChannels,
            visitTopTypes,
            crimeTypes,
            mapData,
            overview,
            visitFields,
            personData,
            caseAnalysis
        ] = await Promise.all([
            getVisitChannelAnalysis(dateRange),
            getVisitTopTypeAnalysis(dateRange),
            getCrimeTypeAnalysis(dateRange),
            getMapData(dateRange),
            getVisitOverview(dateRange),
            getVisitFieldAnalysis(dateRange),
            getPersonAnalysis(dateRange),
            getCaseAnalysis(dateRange)
        ])
        
        return {
            visitChannels,
            visitTopTypes,
            crimeTypes,
            mapData,
            overview,
            visitFields,
            personData,
            caseAnalysis
        }
    } catch (error) {
        console.error('获取所有风险管理数据失败:', error)
        
        // 返回空数据结构
        return {
            visitChannels: {
                items: []
            },
            visitTopTypes: {
                top1: { key: 'top1', count: "0", label: "重复访" },
                top2: { key: 'top2', count: "0", label: "越级访" },
                top3: { key: 'top3', count: "0", label: "缠闹访" },
                top4: { key: 'top4', count: "0", label: "极端访" },
                top5: { key: 'top5', count: "0", label: "集体访" }
            },
            crimeTypes: {
                items: []
            },
            mapData: {
                areas: []
            },
            overview: {
                totalVisits: "0",
                yearOnYearGrowth: {
                    value: "0%",
                    isIncrease: false
                },
                guangzhouVisits: "0",
                guangzhouGrowth: {
                    value: "0%",
                    isIncrease: false
                }
            },
            visitFields: [],
            personData: [],
            caseAnalysis: {
                data: []
            }
        }
    }
} 