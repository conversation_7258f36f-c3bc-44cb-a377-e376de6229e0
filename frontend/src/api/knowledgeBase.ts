import request from './request'
import { DataResult, PageResult } from './commons'
import { SSE } from 'sse.js'

export interface HistoryChats {
    
    chat_id: string;
    
    /* */
    create_date: string;

    /* */
    create_time: number;

    /* */
    id: string;

    /* */
    messages: {
      /* */
      content: string;

      /* */
      role: string;
    }[];

    /* */
    name: string;

    /* */
    update_date: string;

    /* */
    update_time: number;

}

export interface ChatSession {
    /* */
    chat_id: string;

    /* */
    create_date: string;

    /* */
    create_time: number;

    /* */
    id: string;

    /* */
    messages: {
      /* */
      content: string;

      /* */
      role: string;
    }[];

    /* */
    name: string;

    /* */
    update_date: string;

    /* */
    update_time: number;
};

// SSE聊天相关的类型定义
export interface ChatMessage {
    role: 'user' | 'bot';
    content: string;
}

export interface ChatStreamCallbacks {
    onMessage?: (content: string) => void;
    onError?: (error: Error) => void;
    onComplete?: () => void;
}
  
export const listHistoryChats = async (currPage: number, pageSize: number): Promise<PageResult<HistoryChats[]>> => {
    // 获取历史聊天记录可能数据量较大，设置20秒超时
    const response = await request.post('/knowledgeBase/listHistoryChat', {
        currPage: currPage,
        pageSize: pageSize
    }, { timeout: 20000 }).then(res => {
        return res.data
    })
    return response
}

/**
 * 发送聊天消息并处理SSE流式响应
 * @param message 用户消息内容
 * @param callbacks 回调函数集合
 * @returns SSE连接实例，可用于手动关闭连接
 */
export const sendChatMessage = (sessionId: string, message: string, callbacks: ChatStreamCallbacks): SSE => {
    const { onMessage, onError, onComplete } = callbacks;
    
    const source = new SSE(`${import.meta.env.VITE_API_BASE_URL || ''}/api/knowledgeBase/chat`, {
        headers: {
            'Content-Type': 'application/json',
            // 添加认证信息
            'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        method: 'POST',
        payload: JSON.stringify({ sessionId, message })
    });

    source.addEventListener('message', (event: MessageEvent) => {
        try {
            const data = JSON.parse(event.data);
            if (onMessage && data.v) {
                onMessage(data.v);
            }
        } catch (error) {
            console.error('解析SSE消息失败:', error);
            if (onError) {
                onError(new Error('解析消息失败'));
            }
        }
    });

    source.addEventListener('error', (error: Event) => {
        console.error('SSE连接错误:', error);
        if (onError) {
            onError(new Error('连接失败，请检查网络或稍后重试'));
        }
        source.close();
    });

    source.addEventListener('close', () => {
        if (onComplete) {
            onComplete();
        }
    });

    // 启动SSE连接
    try {
        source.stream();
    } catch (error) {
        console.error('启动SSE连接失败:', error);
        if (onError) {
            onError(new Error('无法建立连接，请稍后重试'));
        }
    }

    return source;
}

/**
 * 创建新的聊天会话
 * @param sessionName 会话标题，可选
 * @returns 新会话的ID
 */
export const createNewChatSession = async (sessionName?: string): Promise<DataResult<ChatSession>> => {
    // 创建聊天会话可能需要更长的处理时间，设置30秒超时
    const response = await request.post('/knowledgeBase/createChat', {
        sessionName: sessionName || `新对话 ${new Date().toLocaleString()}`
    }, { timeout: 30000 });
    return response.data;
}

/**
 * 更新对话标题
 * @param sessionId 会话ID
 * @param title 新标题
 * @returns 是否更新成功
 */
export const updateChatTitle = async (sessionId: string, sessionName: string): Promise<boolean> => {
    try {
        const response = await request.post('/knowledgeBase/updateChat', {
            sessionId,
            sessionName
        });
        return response.data.code === 0;
    } catch (error) {
        console.error('更新对话标题失败:', error);
        return false;
    }
}

/**
 * 删除对话
 * @param sessionId 会话ID
 * @returns 是否删除成功
 */
export const deleteChatSession = async (sessionId: string): Promise<boolean> => {
    try {
        const response = await request.post('/knowledgeBase/deleteChat', {
            sessionId
        });
        return response.data.code === 0;
    } catch (error) {
        console.error('删除对话失败:', error);
        return false;
    }
}