import { defineStore } from 'pinia'

interface UserState {
  token: string | null
  name: string | null
  loginId: string | null
  isAuthenticated: boolean
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: localStorage.getItem('token'),
    name: localStorage.getItem('userName'),
    loginId: localStorage.getItem('userLoginId'),
    isAuthenticated: !!localStorage.getItem('token')
  }),
  
  actions: {
    setUserInfo(data: { token: string, name: string, loginId: string }) {
      this.token = data.token
      this.name = data.name
      this.loginId = data.loginId
      this.isAuthenticated = true
      
      // 保存到本地存储
      localStorage.setItem('token', data.token)
      localStorage.setItem('userName', data.name)
      localStorage.setItem('userLoginId', data.loginId)
    },
    
    logout() {
      this.token = null
      this.name = null
      this.loginId = null
      this.isAuthenticated = false
      
      // 清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('userName')
      localStorage.removeItem('userLoginId')
    }
  }
}) 